"""
Pydantic schemas for standardized output formats across the voice agent platform.

This module defines the StateOutput schema that enforces a consistent output format
for all vertical pipeline steps in the voice agent system.
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, Optional, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict


class StatusType(str, Enum):
    """Enumeration of possible status values for StateOutput."""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    PENDING = "pending"
    PROCESSING = "processing"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class StatusCode(int, Enum):
    """HTTP-like status codes for StateOutput responses."""
    # Success codes (2xx)
    OK = 200
    CREATED = 201
    ACCEPTED = 202
    NO_CONTENT = 204

    # Client error codes (4xx)
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    TIMEOUT_ERROR = 408
    CONFLICT = 409
    VALIDATION_ERROR = 422

    # Server error codes (5xx)
    INTERNAL_ERROR = 500
    NOT_IMPLEMENTED = 501
    SERVICE_UNAVAILABLE = 503
    GATEWAY_TIMEOUT = 504

    # Custom agent-specific codes (6xx)
    AGENT_ERROR = 600
    PIPELINE_ERROR = 601
    MEMORY_ERROR = 602
    AUDIO_ERROR = 603
    LLM_ERROR = 604
    RAG_ERROR = 605


class StateOutput(BaseModel):
    """
    Standardized output format for all vertical pipeline steps.
    
    This schema ensures consistent response structure across all agents
    and pipeline components in the voice agent platform.
    
    Attributes:
        status: The execution status (success, error, warning, etc.)
        message: Human-readable description of the result
        code: HTTP-like status code for programmatic handling
        outputs: Dictionary containing the actual output data
        meta: Dictionary containing metadata about the execution
    
    Example:
        ```python
        output = StateOutput(
            status="success",
            message="Audio successfully processed and transcribed",
            code="200_OK",
            outputs={
                "transcript": "Hello, how can I help you today?",
                "confidence": 0.95,
                "language": "en-US"
            },
            meta={
                "agent": "audio_agent",
                "processing_time_ms": 150,
                "model_used": "whisper-large-v3"
            }
        )
        ```
    """
    
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=False,  # Only validate at creation, not assignment
        extra="forbid"
    )
    
    status: StatusType = Field(
        ...,
        description="Execution status indicating success, error, or other states"
    )
    
    message: str = Field(
        ...,
        min_length=1,
        max_length=1000,
        description="Human-readable message describing the result or error"
    )
    
    code: StatusCode = Field(
        ...,
        description="HTTP-like status code for programmatic response handling"
    )
    
    outputs: Dict[str, Any] = Field(
        default_factory=dict,
        description="Dictionary containing the actual output data from the operation"
    )
    
    meta: Dict[str, Any] = Field(
        default_factory=dict,
        description="Dictionary containing metadata about the execution context"
    )
    
    # Keep only essential validators
    @model_validator(mode='after')
    def validate_status_code_consistency(self):
        """Ensure status and code are consistent with each other."""
        if self.status == StatusType.SUCCESS and not (200 <= self.code.value < 300):
            raise ValueError("Success status must have 2xx status code")
        elif self.status == StatusType.ERROR and (200 <= self.code.value < 300):
            raise ValueError("Error status cannot have 2xx status code")
        return self
    
    def is_success(self) -> bool:
        """Check if the operation was successful."""
        return self.status == StatusType.SUCCESS
    
    def is_error(self) -> bool:
        """Check if the operation resulted in an error."""
        return self.status == StatusType.ERROR
    
    def get_output(self, key: str, default: Any = None) -> Any:
        """Safely get a value from the outputs dictionary."""
        return self.outputs.get(key, default)
    
    def get_meta(self, key: str, default: Any = None) -> Any:
        """Safely get a value from the meta dictionary."""
        return self.meta.get(key, default)
    
    def add_output(self, key: str, value: Any) -> None:
        """Add or update an output value."""
        self.outputs[key] = value
    
    def add_meta(self, key: str, value: Any) -> None:
        """Add or update a meta value."""
        self.meta[key] = value


class AgentStateOutput(StateOutput):
    """
    Extended StateOutput specifically for agent responses.
    
    Includes additional fields commonly used by voice agents.
    """
    
    def __init__(self, **data):
        super().__init__(**data)
        # Ensure agent-specific meta fields are present
        if 'agent_name' not in self.meta:
            self.meta['agent_name'] = 'unknown'
        if 'session_id' not in self.meta:
            self.meta['session_id'] = None


class PipelineStateOutput(StateOutput):
    """
    Extended StateOutput for pipeline step responses.
    
    Includes pipeline-specific metadata.
    """
    
    def __init__(self, **data):
        super().__init__(**data)
        # Ensure pipeline-specific meta fields are present
        if 'step_name' not in self.meta:
            self.meta['step_name'] = 'unknown'
        if 'step_index' not in self.meta:
            self.meta['step_index'] = -1
        if 'pipeline_id' not in self.meta:
            self.meta['pipeline_id'] = None


# Convenience factory functions for common response patterns

def success_output(
    message: str,
    outputs: Optional[Dict[str, Any]] = None,
    meta: Optional[Dict[str, Any]] = None,
    code: StatusCode = StatusCode.OK
) -> StateOutput:
    """Create a success StateOutput with default values."""
    return StateOutput(
        status=StatusType.SUCCESS,
        message=message,
        code=code,
        outputs=outputs or {},
        meta=meta or {}
    )


def error_output(
    message: str,
    code: StatusCode = StatusCode.INTERNAL_ERROR,
    outputs: Optional[Dict[str, Any]] = None,
    meta: Optional[Dict[str, Any]] = None
) -> StateOutput:
    """Create an error StateOutput with default values."""
    return StateOutput(
        status=StatusType.ERROR,
        message=message,
        code=code,
        outputs=outputs or {},
        meta=meta or {}
    )


def warning_output(
    message: str,
    outputs: Optional[Dict[str, Any]] = None,
    meta: Optional[Dict[str, Any]] = None,
    code: StatusCode = StatusCode.OK
) -> StateOutput:
    """Create a warning StateOutput with default values."""
    return StateOutput(
        status=StatusType.WARNING,
        message=message,
        code=code,
        outputs=outputs or {},
        meta=meta or {}
    )
