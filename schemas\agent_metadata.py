from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Any
from agents.base.base_agent import BaseAgent

class AgentMetadata(BaseModel):
    """
    A Pydantic model to define the structure for an agent's metadata.
    This ensures consistency when agents register themselves.
    """
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    agent_name: str = Field(..., description="The unique name of the agent (e.g., 'STTAgent').")
    version: str = Field(..., description="The version of the agent.")
    description: str = Field(..., description="A brief description of the agent's purpose.")

    status: str = Field(default="starting", description="The current status of the agent (e.g., 'healthy', 'unhealthy', 'starting').")
    
    # Optional field for agents that are external services
    # endpoint: Optional[str] = Field(None, description="The network endpoint if the agent is an external service.")
    # capabilities: List[str] = Field(default_factory=list, description="A list of skills the agent possesses (e.g., 'transcription', 'intent_parsing').")
    entrypoint: Optional[str] = Field(None, description="The entrypoint script or module for the agent.")
    methods: Optional[List[str]] = Field(default_factory=list, description="List of methods the agent exposes.")
    guard_rails: Optional[Any] = Field(None, description="Guard rails for output validation (string or list of strings).")

    def to_json(self) -> str:
        return self.model_dump_json()

    @classmethod
    def from_json(cls, json_str: str):
        return cls.model_validate_json(json_str) 
