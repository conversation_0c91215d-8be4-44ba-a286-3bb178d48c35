# Modular Interrupt Handling System Documentation

## **1. 📝 Feature Overview**

**Description**: The modular interrupt handling system provides a dedicated, separate state (`InterruptState`) that works alongside the existing `TTSState` to handle voice interrupts during TTS playback. This architecture maintains clear separation of concerns while enabling sophisticated interrupt detection, confirmation, and context-aware response generation.

**Key Components**:
- **TTSState**: Focuses solely on TTS audio generation
- **InterruptState**: Dedicated interrupt detection and handling
- **StateManager**: Coordinates between states and manages interrupt workflow
- **MemoryManager**: Stores interrupt context for state coordination

**Target Audience**: 
- **Voice AI Developers**: Teams implementing interrupt handling in voice agents
- **System Architects**: Teams designing modular voice AI architectures
- **QA Engineers**: Teams testing interrupt functionality
- **Product Managers**: Teams planning voice interaction features

## **2. 🏗️ Architecture Overview**

### Component Responsibilities

**TTSState (`core/state_manager/state_output.py`)**
- Text-to-speech audio generation
- Agent integration for TTS processing
- Clean, focused responsibility without interrupt complexity

**InterruptState (`core/state_manager/state_output.py`)**
- Voice Activity Detection (VAD) using WebRTC VAD with energy-based fallback
- Grace period confirmation (0.5s default) to prevent false alarms
- Action reversibility analysis from context configuration
- Context-aware acknowledgment message generation
- Memory context storage for StateManager coordination

**StateManager (`core/state_manager/state_manager.py`)**
- Coordinates between TTSState and InterruptState
- Monitors TTS playback and starts interrupt detection
- Processes interrupt events and manages workflow transitions
- Handles interrupt context from memory and orchestrates responses

### Integration Points

```python
# StateManager pipeline_state_map includes InterruptState
self.pipeline_state_map = {
    "stt_process": STTState,
    "preprocessing_process": PreProcessingState,
    "processing_process": ProcessingState,
    "filler_tts_process": FillerState,
    "tts_process": TTSState,
    "interrupt_process": InterruptState,  # New addition
}
```

## **3. 🔄 Interrupt Handling Flow**

### Step-by-Step Process

1. **TTS Execution**: StateManager executes TTSState for audio generation
2. **Interrupt Monitoring Setup**: StateManager starts interrupt monitoring after successful TTS
3. **Voice Detection**: InterruptState monitors audio input using VAD
4. **Grace Period Confirmation**: 0.5-second confirmation window validates genuine interrupts
5. **Reversibility Analysis**: Context analysis determines interrupt handling strategy
6. **Acknowledgment Generation**: Context-aware response based on action reversibility
7. **Memory Storage**: Interrupt context stored for StateManager coordination
8. **Flow Management**: StateManager handles resume/queue logic based on reversibility

### Action Reversibility Strategies

**Reversible Actions**
- **Message**: "Allow me to finish this first, then I'll respond to what you said."
- **Behavior**: Resume TTS → Process queued user input
- **Use Cases**: Information queries, menu navigation, preference changes

**Irreversible Actions**
- **Message**: "The action has already been completed. If something went wrong, let me know and I'll help fix it."
- **Behavior**: Complete TTS → No input queuing (action already executed)
- **Use Cases**: Financial transfers, data deletions, external API calls

**Unknown Reversibility**
- **Message**: "I understand you want to say something. Allow me to finish this first, then I'll respond to what you said."
- **Behavior**: Default to reversible behavior for safety
- **Use Cases**: Actions without explicit reversibility configuration

## **4. 💻 Implementation Details**

### InterruptState Core Methods

**Voice Activity Detection**
```python
async def _detect_voice_activity(self, audio_data: bytes) -> bool:
    # Uses AudioProcessor with WebRTC VAD
    # Falls back to energy-based detection
    # Returns True if voice activity detected
```

**Grace Period Confirmation**
```python
async def _confirm_interrupt_with_grace_period(self) -> bool:
    # Waits for confirmation window (default 0.5s)
    # Validates continued voice activity
    # Returns True if interrupt confirmed
```

**Action Reversibility Analysis**
```python
def _analyze_action_reversibility(self, context) -> str:
    # Analyzes workflow_state_config
    # Checks explicit_reversibility in context
    # Returns "reversible", "irreversible", or "unknown"
```

**Memory Context Storage**
```python
async def _store_interrupt_context(self, input_data, action_reversible, acknowledgment, context):
    # Stores interrupt context in memory
    # Saves TTS playback state for resume
    # Logs interrupt event for history
```

### StateManager Integration Methods

**TTS Interrupt Monitoring**
```python
async def _start_tts_interrupt_monitoring(self, tts_result: StateOutput):
    # Sets up interrupt monitoring after TTS execution
    # Stores TTS playback state in memory
    # Prepares for interrupt detection
```

**Interrupt Detection Processing**
```python
async def process_interrupt_detection(self, audio_data: bytes, current_tts_audio_path: str, playback_position: float):
    # Creates InterruptState instance
    # Processes interrupt with context
    # Returns interrupt handling results
```

## **5. ⚙️ Configuration**

### Interrupt Configuration

```python
interrupt_config = InterruptConfig(
    detection=InterruptDetectionConfig(
        enabled=True,
        vad_threshold=0.01,                    # Voice detection sensitivity
        confirmation_window_seconds=0.5,       # Grace period for confirmation
        min_interrupt_duration_seconds=0.3     # Minimum duration for valid interrupt
    )
)
```

### Context Configuration for Reversibility

```python
# In workflow state configuration
context = {
    "workflow_state_config": {
        "reversible": True,                    # Explicit reversibility flag
        "has_side_effect": False,             # Side effect indicator
        "post_tts_policy": "allow_interrupt"  # Interrupt policy
    }
}
```

## **6. 🧪 Testing**

### Integration Test Coverage

**Basic Functionality Tests**
- InterruptState creation and configuration
- Voice activity detection with and without audio
- Action reversibility analysis for all scenarios
- Memory context storage and retrieval

**Interrupt Flow Tests**
- Complete interrupt flow for reversible actions
- Complete interrupt flow for irreversible actions
- False alarm handling and grace period validation
- Error handling and edge cases

**StateManager Coordination Tests**
- TTS interrupt monitoring setup
- Interrupt detection processing
- Memory state management
- End-to-end workflow coordination

### Test Execution

```bash
# Run comprehensive integration tests
python tests/integration/test_interrupt_handling_system.py

# Run interactive demo
python tests/integration/run_interrupt_tests.py
```

## **7. 🚀 Usage Examples**

### Basic InterruptState Usage

```python
# Create InterruptState
interrupt_state = InterruptState(
    state_id="interrupt_handler",
    agent_registry=agent_registry,
    session_id=session_id,
    interrupt_config=interrupt_config
)

# Process interrupt detection
input_data = {
    "audio_data": audio_bytes,
    "current_tts_audio_path": "/path/to/tts.mp3",
    "playback_position": 2.5,
    "user_input": "User's interrupt text"
}

context = {
    "workflow_state_config": {"reversible": True},
    "memory_manager": memory_manager
}

result = await interrupt_state.process(input_data, context)
```

### StateManager Integration

```python
# StateManager automatically handles TTS interrupt coordination
state_manager = await StateManager.create("workflow.json", session_id, user_id)

# TTS execution automatically sets up interrupt monitoring
tts_result = await state_manager.execute_step({"text": "Hello, how can I help you?"})

# Process interrupt when detected
interrupt_result = await state_manager.process_interrupt_detection(
    audio_data=microphone_input,
    current_tts_audio_path=tts_result.outputs["audio_path"],
    playback_position=current_position
)
```

## **8. 🔧 Extension Points**

### Custom Reversibility Logic

```python
class CustomInterruptState(InterruptState):
    def _analyze_action_reversibility(self, context):
        # Custom business logic for reversibility
        if context.get("transaction_amount", 0) > 1000:
            return "irreversible"
        return super()._analyze_action_reversibility(context)
```

### Custom Acknowledgment Messages

```python
class CustomInterruptState(InterruptState):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.interrupt_messages = {
            "reversible": "I'll pause here and address your question.",
            "irreversible": "That action is complete, but I'm here to help with anything else.",
            "unknown": "Let me finish this thought, then I'll help you."
        }
```

## **9. 📊 Benefits**

### Architectural Benefits
- **Separation of Concerns**: Clear boundaries between TTS and interrupt handling
- **Modularity**: InterruptState can be used independently or with StateManager
- **Testability**: Each component can be tested in isolation
- **Maintainability**: Changes to interrupt logic don't affect TTS functionality

### Functional Benefits
- **Sophisticated Interrupt Handling**: VAD, grace periods, and context awareness
- **Flexible Configuration**: Adjustable thresholds and timing parameters
- **Context-Aware Responses**: Different acknowledgments based on action reversibility
- **Memory Integration**: Seamless coordination with StateManager through memory

### Development Benefits
- **Clear APIs**: Well-defined interfaces between components
- **Easy Extension**: Simple to add custom logic or new interrupt strategies
- **Comprehensive Testing**: Full test coverage with integration and unit tests
- **Documentation**: Complete documentation with examples and usage patterns
