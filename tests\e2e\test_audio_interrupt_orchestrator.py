import asyncio
import os
import sys
from dotenv import load_dotenv

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from core.session_manager_v2 import SessionManagerV2
from utils.audio_utils import detect_voice_activity

load_dotenv()

# Set optimal interrupt detection environment variables for real-time demo
os.environ.setdefault('VAD_THRESHOLD', '0.05')  # Sensitive voice detection
os.environ.setdefault('VAD_METHOD', 'webrtcvad')  # Use WebRTC VAD for best performance
os.environ.setdefault('WEBRTC_AGGRESSIVENESS', '3')  # Maximum aggressiveness
os.environ.setdefault('TTS_INTERRUPT_COOLDOWN_SECONDS', '0.0')  # Immediate interrupts
os.environ.setdefault('SIMPLE_INTERRUPT_CONFIRMATION', 'true')  # Simplified confirmation for demo

async def select_input_device():
    """Select microphone input device for recording."""
    import sounddevice as sd
    devices = sd.query_devices()
    input_devices = [(i, d) for i, d in enumerate(devices) if d['max_input_channels'] > 0]
    print("Available input devices:")
    for idx, dev in input_devices:
        print(f"  [{idx}] {dev['name']} (inputs: {dev['max_input_channels']})")
    while True:
        try:
            device_index = int(input("Enter the device index for your microphone: ").strip())
            if any(idx == device_index for idx, _ in input_devices):
                return device_index
            else:
                print("Invalid index. Please select from the list above.")
        except Exception:
            print("Please enter a valid integer index.")

async def record_microphone_audio(duration_sec=5, sample_rate=16000, device_index=None):
    """Record audio from microphone using VAD for voice activity detection."""
    import sounddevice as sd
    import wave
    import time

    print(f"🎤 [MIC] Recording {duration_sec} seconds of audio...")
    print("🗣️  Speak now!")

    # Record audio from microphone
    audio = sd.rec(int(duration_sec * sample_rate), samplerate=sample_rate, channels=1, dtype='int16', device=device_index)
    sd.wait()

    # Create timestamped filename
    timestamp = int(time.time())
    audio_filename = f'recorded_audio_{timestamp}.wav'

    # Save audio to WAV file
    audio_bytes = audio.tobytes()
    with wave.open(audio_filename, 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(sample_rate)
        wf.writeframes(audio_bytes)

    # Use VAD to check if voice was detected
    vad_result = detect_voice_activity(audio_bytes)
    has_voice = vad_result.outputs.get("has_voice", False)

    print(f"✅ [INFO] Saved recorded audio to: {audio_filename}")
    print(f"🔍 [VAD] Voice detected: {has_voice}")

    return audio_filename

async def get_audio_input_choice():
    """Get user choice for audio input method."""
    print("\n🎯 Audio Input Options:")
    print("1. Use microphone input (live recording)")
    print("2. Use specified audio file path")

    while True:
        try:
            choice = input("Enter your choice (1 or 2): ").strip()
            if choice in ['1', '2']:
                return choice
            else:
                print("Invalid choice. Please enter 1 or 2.")
        except Exception:
            print("Please enter a valid choice (1 or 2).")

async def run_streamlined_voice_workflow():
    """
    Run a streamlined single-flow voice workflow test with two audio input options:
    1. Microphone input (live recording)
    2. Specified audio file path

    Flow: Greeting TTS → User speaks → Processing → Response TTS → End
    """
    print("🎯 Streamlined Voice Workflow Test")
    print("=" * 50)

    # Get audio input choice
    choice = await get_audio_input_choice()

    audio_path = None
    device_index = None

    if choice == "1":
        # Option 1: Microphone input
        device_index = await select_input_device()
        print("✅ Microphone input selected")
    else:
        # Option 2: Audio file path
        while True:
            file_path = input("Enter path to audio file (.wav or .mp3): ").strip()
            if os.path.exists(file_path):
                audio_path = file_path
                print(f"✅ Audio file selected: {audio_path}")
                break
            else:
                print("❌ File not found. Please enter a valid path.")

    # Initialize SessionManagerV2
    session_manager = SessionManagerV2()
    workflow_name = 'banking_workflow_v2.json'
    user_id = 'streamlined_voice_user'

    try:
        # Create a new session
        session_id = await session_manager.create_session(workflow_name, user_id)
        print(f"✅ Session created: {session_id}")

        # Retrieve the memory manager for the session
        memory_manager = session_manager.active_sessions[session_id]["memory_manager"]

        # Initialize orchestrator
        orchestrator = await session_manager.initialize_orchestrator(session_id)

        print("\n🎭 Starting streamlined voice workflow...")
        print("📋 Flow: Greeting TTS → User speaks → Processing → Response TTS → End")
        print()

        # Step 1: Greeting State - TTS only (no audio override)
        print("🤖 [STEP 1] Playing greeting...")
        print("   ⏳ Please wait for greeting to complete before speaking")

        result = await orchestrator.run()
        print(f"✅ [STEP 1] Greeting completed: {result.get('status', 'unknown')}")

        # Step 2: User Input - After greeting completes
        print("\n🎤 [STEP 2] User input phase...")

        if choice == "1":
            # Record from microphone
            print("   🗣️  Speak now (after greeting finished)...")
            user_audio_path = await record_microphone_audio(device_index=device_index)
        else:
            # Use provided audio file
            user_audio_path = audio_path
            print(f"   📁 Using audio file: {user_audio_path}")

        # Clear any existing audio_path and set user's recorded audio
        print(f"[DEBUG] Clearing any existing audio_path from memory")
        await memory_manager.delete("audio_path")
        print(f"[DEBUG] Setting user audio_path in memory to: {user_audio_path}")
        await memory_manager.set("contextual", "audio_path", user_audio_path)

        # Step 3: Processing - STT → Intent → Agent → TTS
        print("\n🤖 [STEP 3] Processing user request...")
        print("   📝 STT: Converting speech to text...")
        print("   🧠 Intent: Analyzing request...")
        print("   ⚙️  Agent: Processing request...")
        print("   🔊 TTS: Generating response...")

        result = await orchestrator.run()

        # Step 4: Results
        print(f"\n✅ [STEP 4] Workflow completed!")
        print(f"   Status: {result.get('status', 'unknown')}")
        if result.get('reason'):
            print(f"   Details: {result.get('reason')}")

        print("\n🎉 Single-flow test completed successfully!")

    except Exception as e:
        print(f"❌ Error in workflow: {e}")
        import traceback
        traceback.print_exc()

    finally:

        # Clean up the session
        await session_manager.cleanup_session(session_id, reason="streamlined_test_complete")
        print("✅ Session cleaned up. Test complete!")

async def update_orchestrator_for_audio_retrieval():
    """
    Update the orchestrator to retrieve audio path from memory manager.
    This demonstrates the integration pattern you requested.
    """
    print("\n📋 Orchestrator Integration Pattern:")
    print("   The orchestrator should retrieve audio path using:")
    print("   audio_path = await self.memory_manager.get('audio_path')")
    print("   print(f'[DEBUG] STT step - checking for audio. self.memory_manager.get(\"audio_path\")')")
    print("   if audio_path:")
    print("       input_data = {'audio_path': audio_path}")
    print()
    print("   This pattern ensures the orchestrator gets audio from memory")
    print("   instead of hardcoded paths, enabling flexible audio input.")
    print()

if __name__ == "__main__":
    import sys
    import os
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))
    print("🚀 Starting Streamlined Voice Workflow Test")
    print("This test demonstrates a simplified single-flow voice pipeline with:")
    print("  ✅ Two audio input options (microphone or file)")
    print("  ✅ Complete STT → Intent → Agent → TTS workflow")
    print("  ✅ VAD integration for voice activity detection")
    print("  ✅ Memory manager audio path retrieval")
    print("  ✅ Proper greeting/ending state handling")
    print()

    # Show orchestrator integration pattern
    asyncio.run(update_orchestrator_for_audio_retrieval())

    # Run the streamlined test
    asyncio.run(run_streamlined_voice_workflow())