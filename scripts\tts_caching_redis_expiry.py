import hashlib
import os
import redis

r = redis.StrictRedis(host="localhost", port=6379, db=0)
pubsub = r.pubsub()
pubsub.psubscribe("__keyevent@0__:expired")

def generate_safe_filename(key: str) -> str:
    # Hash the key
    hashed_key = hashlib.sha256(key.encode()).hexdigest()
    return f"tts_output_{hashed_key}.mp3"

for message in pubsub.listen():
    print("delete tts file associated to key: ", message["data"])
    key = message["data"]
    if not isinstance(key, bytes):
        continue
    key = key.decode("utf-8")
    fileName = generate_safe_filename(key)
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../'))
    audio_path = os.path.join(project_root,fileName)
    print("audio_path: ", audio_path)
    if os.path.exists(audio_path):
        os.remove(audio_path)
        print("Deleted audio file: ", audio_path)
    else:
        print("Audio file not found: ", audio_path)