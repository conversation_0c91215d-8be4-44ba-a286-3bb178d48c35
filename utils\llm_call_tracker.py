"""
Universal LLM Token Tracker with Accurate Audio Cost Calculation and Model-Compatible Tokenizers

One simple function to track any LLM call - just pass the model name and response data.
Automatically detects provider and handles token counting and cost calculation.

For audio models (STT/TTS), provides multiple methods for accurate cost calculation:
1. Actual audio duration (most accurate)
2. File size estimation (good approximation)
3. Text length estimation (fallback)

For text models, uses the correct tokenizer for each model (tiktoken for OpenAI, transformers for LLaMA, etc.).
"""

from datetime import datetime
from typing import Dict, Any, Optional, Union
import os
import json
from core.logging.logger_config import get_module_logger

logger = get_module_logger("llm_call_tracker")

# Load model pricing from JSON file with error handling
MODEL_PRICING_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'configs', 'model_pricing.json')
try:
    with open(MODEL_PRICING_PATH, 'r', encoding='utf-8') as f:
        MODEL_PRICING = json.load(f)
except Exception as e:
    logger.error(
        "Failed to load model pricing JSON",
        action="load_model_pricing",
        reason=str(e),
        layer="llm_call_tracker",
        input_data={"path": MODEL_PRICING_PATH}
    )
    MODEL_PRICING = {}

def count_tokens(text: str, model: str) -> int:
    model = model.lower()
    try:
        if model.startswith("gpt-4o") or model.startswith("gpt-4") or model.startswith("gpt"):
            import tiktoken
            encoding_map = {
                "gpt-4o-mini": "o200k_base",
                "gpt-4o": "o200k_base",
                "gpt-4": "cl100k_base",
                "gpt-3.5-turbo": "cl100k_base"
            }
            encoding_name = encoding_map.get(model, "cl100k_base")
            encoding = tiktoken.get_encoding(encoding_name)
            return len(encoding.encode(text))
        elif "llama" in model:
            from transformers import AutoTokenizer
            # You may want to map model names to HF repo names
            tokenizer = AutoTokenizer.from_pretrained("meta-llama/Llama-2-7b-chat-hf")
            return len(tokenizer.encode(text))
        # Add more model-specific logic as needed
    except Exception as e:
        logger.error(
            "Token counting failed",
            action="count_tokens",
            reason=str(e),
            layer="llm_call_tracker",
            input_data={"model": model, "text_preview": text[:30] if text else ""}
        )
    # Fallback
    return max(1, len(text) // 4)

def get_audio_duration(audio_path: str = None, audio_data: bytes = None) -> float:
    """
    Attempts to get audio duration using pydub (ffmpeg) first, then mutagen, then file size estimation.
    """
    try:
        # Try pydub (ffmpeg)
        try:
            from pydub import AudioSegment
            if audio_path and os.path.exists(audio_path):
                print("Trying pydub with audio_path:", audio_path)
                audio = AudioSegment.from_file(audio_path)
                print("Used pydub for duration")
                return len(audio) / 1000.0
            elif audio_data:
                print("Trying pydub with audio_data")
                import tempfile
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp:
                    tmp.write(audio_data)
                    tmp.flush()
                    temp_path = tmp.name
                # File is now closed, safe for AudioSegment
                audio = AudioSegment.from_file(temp_path)
                os.unlink(temp_path)
                print("Used pydub for duration (bytes)")
                return len(audio) / 1000.0
        except Exception as e:
            print("pydub failed:", e)
            # Try mutagen as a fallback
            try:
                from mutagen.mp3 import MP3
                if audio_path and os.path.exists(audio_path):
                    print("Trying mutagen with audio_path:", audio_path)
                    audio = MP3(audio_path)
                    print("Used mutagen for duration")
                    return audio.info.length
                elif audio_data:
                    print("Trying mutagen with audio_data")
                    import tempfile
                    with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp:
                        tmp.write(audio_data)
                        tmp.flush()
                        temp_path = tmp.name
                    # File is now closed, safe for mutagen
                    audio = MP3(temp_path)
                    os.unlink(temp_path)
                    print("Used mutagen for duration (bytes)")
                    return audio.info.length
            except Exception as e2:
                print("mutagen failed:", e2)
        # Fallback to file size estimation
        if audio_path and os.path.exists(audio_path):
            print("Falling back to file size estimation")
            file_size = os.path.getsize(audio_path)
            return max(1.0, file_size / 1024)
        elif audio_data:
            print("Falling back to data size estimation")
            return max(1.0, len(audio_data) / 1024)
        print("Falling back to default duration")
        return 10.0
    except Exception as e:
        print("get_audio_duration outer exception:", e)
        logger.error(
            "Audio duration estimation failed",
            action="get_audio_duration",
            reason=str(e),
            layer="llm_call_tracker",
            input_data={"audio_path": audio_path, "audio_data_len": len(audio_data) if audio_data else None}
        )
        return 10.0


def extract_response_text(response, model: str = "") -> str:
    try:
        if hasattr(response, 'choices') and response.choices:
            return response.choices[0].message.content.strip()
        if hasattr(response, 'text'):
            return response.text.strip()
        if isinstance(response, str):
            return response.strip()
        return str(response)
    except Exception as e:
        logger.error(
            "Failed to extract response text",
            action="extract_response_text",
            reason=str(e),
            layer="llm_call_tracker",
            input_data={"model": model, "response_type": str(type(response))}
        )
        return ""

def calculate_cost(model: str, input_tokens: int = 0, output_tokens: int = 0,
                  duration_sec: float = 0, character_count: int = 0) -> float:
    pricing = MODEL_PRICING.get(model.lower(), {})
    model_type = pricing.get("type", "text")
    if model_type == "stt":
        duration_minutes = duration_sec / 60.0
        cost_per_minute = pricing.get("cost_per_minute", 0)
        return duration_minutes * cost_per_minute
    elif model_type == "tts":
        cost_per_1k_chars = pricing.get("cost_per_1k_chars", 0)
        return (character_count / 1000.0) * cost_per_1k_chars
    else:
        input_cost = (input_tokens / 1000.0) * pricing.get("input", 0)
        output_cost = (output_tokens / 1000.0) * pricing.get("output", 0)
        return input_cost + output_cost

async def track_llm_call(memory_manager, model: str, input_text: str = "", response_or_output: Any = None,
                        context: str = "llm", audio_path: str = None, audio_data: bytes = None):
    """
    Universal function to track ANY LLM call with accurate cost calculation and model-compatible tokenizers.
    """
    try:
        pricing = MODEL_PRICING.get(model.lower(), {})
        model_type = pricing.get("type", "text")
        if isinstance(response_or_output, str):
            output_text = response_or_output
        else:
            output_text = extract_response_text(response_or_output, model)
        input_tokens = 0
        output_tokens = 0
        duration_sec = 0
        character_count = 0
        cost = 0
        if model_type == "stt":
            duration_sec = get_audio_duration(audio_path, audio_data)
            output_tokens = count_tokens(output_text, model) if output_text else 0
            cost = calculate_cost(model, duration_sec=duration_sec)
        elif model_type == "tts":
            character_count = len(input_text) if input_text else 0
            input_tokens = character_count
            cost = calculate_cost(model, character_count=character_count)
        else:
            input_tokens = count_tokens(input_text, model) if input_text else 0
            output_tokens = count_tokens(output_text, model) if output_text else 0
            cost = calculate_cost(model, input_tokens, output_tokens)
        call_data = {
            "model": model,
            "context": context,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "cost_usd": cost,
            "timestamp": datetime.now().isoformat()
        }
        if model_type == "stt":
            call_data["audio_duration_sec"] = duration_sec
        elif model_type == "tts":
            call_data["character_count"] = character_count
        existing_calls = await memory_manager.contextual.get("llm_calls") or []
        existing_calls.append(call_data)
        if len(existing_calls) > 20:
            existing_calls = existing_calls[-20:]
        await memory_manager.contextual.set("llm_calls", existing_calls)
    except Exception as e:
        logger.error(
            "Failed to track LLM call",
            action="track_llm_call",
            reason=str(e),
            layer="llm_call_tracker",
            input_data={
                "model": model,
                "input_text_preview": input_text[:30] if input_text else "",
                "context": context
            }
        )
