## Overview

The `Logger` class in `core/logger.py` is a custom logging utility built on Python's `logging` module, designed for the Voice Agents Platform. It provides structured JSONL logging with thread-safe concurrent writes, file rotation, and support for custom log fields and application-specific objects like `StateOutput`. This manual covers its purpose, configuration, usage, testing, and troubleshooting.

### Purpose

- **Structured Logging**: Outputs logs in JSON Lines (JSONL) format with fields like `timestamp`, `session_id`, `state_id`, `layer`, `metrics`, and `trace_id` for traceability and analysis.
- **Thread Safety**: Supports concurrent logging in multithreaded environments using `QueueHandler` and `QueueListener`.
- **File Rotation**: Manages log file sizes with automatic rotation to prevent unbounded growth.
- **Custom Features**: Includes a decorator for automatic function logging and methods for logging `StateOutput` objects.
- **Debugging Support**: Tracks processed records and provides detailed debug output for queue and file operations.

### Key Components

- **Logger Class**: The main interface for logging, wrapping a `logging.Logger` instance.
- **J<PERSON>Formatter**: A custom `logging.Formatter` for JSONL output.
- **CountingQueueListener**: A subclass of `logging.handlers.QueueListener` to track processed records.
- **RotatingFileHandler**: Manages log files with size-based rotation.
- **StateOutput**: A mock class for structured output objects (used in tests).

## Configuration

### Initialization

The `Logger` class is initialized with the following parameters:

```python
def __init__(self, log_dir: str = "logs", verbosity: str = "info", max_bytes: int = 10**6, backup_count: int = 5):
```

- **log_dir**: Directory for log files (default: `"logs"`). Subdirectories `conversations`, `metrics`, and `errors` are created automatically. For tests, use `voice_agent_test_logs` in the current directory.
- **verbosity**: Log level (default: `"info"`). Options: `debug`, `info`, `warn`, `error`, `audit` (maps to `logging.DEBUG`, `logging.INFO`, etc.).
- **max_bytes**: Maximum size (in bytes) for each log file before rotation (default: 1MB or `10**6`). Tests use 10000 for frequent rotation.
- **backup_count**: Number of rotated files to keep (default: 5, e.g., `metrics.jsonl.1` to `metrics.jsonl.5`). Tests use 2.

**Example**:

```python
from core.logger import Logger
logger = Logger(log_dir="voice_agent_test_logs", verbosity="debug", max_bytes=10000, backup_count=2)
```

### Log File Structure

Logs are written to three JSONL files in `log_dir`:

- **conversations/conversations.jsonl**: Logs `INFO`level messages (e.g., conversation events).
- **metrics/metrics.jsonl**: Logs `INFO`level messages (e.g., performance metrics).
- **errors/errors.jsonl**: Logs `ERROR`level messages (e.g., exceptions).

Each file rotates when it exceeds `max_bytes`, creating files like `metrics.jsonl.1`, `metrics.jsonl.2`, etc.

### Log Format

Logs are formatted as JSONL using `JsonFormatter`. Each entry includes:

- **timestamp**: ISO 8601 format (e.g., `"2025-06-19T04:13:46Z"`).
- **session_id**: Unique session identifier.
- **state_id**: State identifier for the process.
- **layer**: Application layer (e.g., `"workflow"`).
- **step**: Processing step (e.g., function name).
- **custom_module**: Module name.
- **agent_name**: Agent or component name.
- **action**: Action performed (e.g., function name).
- **input**: Input data (e.g., function arguments).
- **output**: Output data (e.g., function return value or `StateOutput`).
- **status**: `"success"` or `"fail"`.
- **metrics**: Dictionary of metrics (e.g., `{"duration_ms": 10}`).
- **reason**: Error message (if `status="fail"`).
- **memory_layer**: Optional memory layer identifier.
- **trace_id**: Unique trace identifier.
- **next_state_id**: Optional next state identifier.

**Example Log Entry**:

```json
{"timestamp": "2025-06-19T04:13:46Z", "session_id": "session_0", "state_id": "test_state_001", "layer": "test", "step": "test_step", "custom_module": "test_module", "agent_name": "TestAgent", "action": "test_action", "input": {"thread": 0}, "output": {"result": "test"}, "status": "success", "metrics": {"duration_ms": 10}, "trace_id": "uuid"}

```

## Usage

### Logging Methods

1. **log**: General-purpose logging method for structured events.
    
    ```python
    logger.log(
        level="info",
        message="Thread 0 log",
        session_id="session_0",
        state_id="test_state_001",
        layer="test",
        step="test_step",
        custom_module="test_module",
        agent_name="TestAgent",
        action="test_action",
        input_data={"thread": 0},
        output_data={"result": "test"},
        status="success",
        metrics={"duration_ms": 10},
        trace_id="uuid"
    )
    ```
    
2. **log_state_output**: Logs a `StateOutput` object.
    
    ```python
    from core.logger import StateOutput
    state_output = StateOutput(status="success", data={"text": "test"}, metadata={"lang": "en"})
    logger.log_state_output(
        level="info",
        message="Test StateOutput",
        session_id="test_session_123",
        state_id="test_state_001",
        layer="workflow",
        step="test_step",
        custom_module="test_module",
        agent_name="TestAgent",
        action="test_action",
        state_output=state_output,
        input_data={"input": "test"},
        trace_id="uuid"
    )
    ```
    
3. **log_decorator**: Decorator for automatic function logging.
    
    ```python
    @logger.log_decorator
    def sample_function(session_id, state_id):
        return {"result": "success"}
    result = sample_function(session_id="test_session_123", state_id="test_state_001")
    
    ```
    
    - Logs function inputs, outputs, and duration.
    - Handles exceptions, logging them as `status="fail"`.

### Flushing and Closing

- **flush**: Ensures all queued logs are written to disk.
    
    ```python
    logger.flush()
    
    ```
    
    - Waits up to 15 seconds for queues to empty.
    - Use before reading log files to ensure consistency.
- **close**: Closes all handlers and stops `QueueListener` threads.
    
    ```python
    logger.close()
    
    ```
    
    - Flushes logs, stops listeners, and closes files.
    - Use at program termination or test cleanup.

## Testing

The test suite in `tests/logger_test.py` verifies the logger’s behavior. Key tests include:

1. **test_concurrent_logging**:
    - Tests thread-safe logging with 3 threads, each writing 10 logs (total 30 logs).
    - Verifies 30 logs in `metrics.jsonl` and `conversations.jsonl` with 3 unique `session_id`s.
    - Recent Issue: Only 22 logs were read due to missing rotated files (`.jsonl.1`).
2. **test_log_rotation**:
    - Tests file rotation by writing 200 logs with large messages.
    - Verifies rotated files (e.g., `metrics.jsonl.1`) are created.
3. Other Tests:
    - `test_log_success`: Tests decorator with successful function.
    - `test_log_state_output`: Tests `StateOutput` logging.
    - `test_log_error`: Tests error logging with decorator.
    - `test_verbosity_filtering`: Tests log level filtering.

### Running Tests

```powershell
python -m pytest tests/logger_test.py -v
```

### Test Setup

- **Log Directory**: `voice_agent_test_logs` in the current working directory (e.g., `Voice-Agents-Platform/voice_agent_test_logs`).
- **Cleanup**: `tearDown` removes the log directory after each test.

### Recent Fix for `test_concurrent_logging`

- **Issue**: Only 22/30 logs were read, but ~25 logs appeared in `conversations.jsonl.1`.
- **Cause**: `_read_log_file` only read main files (`metrics.jsonl`, `conversations.jsonl`), missing rotated files (`.jsonl.1`) created due to `maxBytes=10000`.
- **Fix**: Updated `_read_log_file` to read all files matching `log_type.jsonl*` (e.g., `metrics.jsonl`, `metrics.jsonl.1`).
- **Outcome**: Test now reads all 30 logs, accounting for rotation.

## Best Practices

1. **Configuration**:
    - Use a local log directory (e.g., `voice_agent_test_logs`) to avoid permissions issues.
    - Set `max_bytes` appropriately (e.g., 10000 for tests, 10**6 for production) based on log volume.
    - Use `verbosity="debug"` for tests and `"info"` or higher for production.
2. **Usage**:
    - Always call `flush()` before reading log files to ensure all logs are written.
    - Use `log_decorator` for functions to automate logging.
    - Include `session_id` and `trace_id` for traceability in distributed systems.
3. **Testing**:
    - Run tests in a local directory to avoid file locking.
    - Check rotated files (`.jsonl.1`) if log counts are lower than expected.
    - Use debug prints (e.g., `record_count`) to verify processed logs.
4. **Concurrency**:
    - The logger is thread-safe due to `QueueHandler` and `QueueListener`.
    - For high concurrency, monitor queue sizes (`qsize`) and file sizes in debug logs.
5. **Cleanup**:
    - Call `close()` in `tearDown` or at program exit to release resources.
    - Handle `PermissionError` during cleanup with retries (as in `tearDown`).

## Troubleshooting

### Common Issues and Solutions

1. **Test Fails with Fewer Logs (e.g., 22/30)**:
    - **Cause**: Missing logs in rotated files.
    - **Solution**: Ensure `_read_log_file` reads all files (`log_type.jsonl*`).
    - **Check**: Print log file names and counts in `_read_log_file`.
2. **Logs Appear During Test but Disappear**:
    - **Cause**: Premature file reading before flush completion.
    - **Solution**: Add `time.sleep(0.5)` after `flush()` or increase flush timeout to 20 seconds:
        
        ```python
        while not handler.queue.empty() and time.time() - start_time < 20:
            time.sleep(0.01)
        
        ```
        
3. **Test Takes Too Long**:
    - **Cause**: Excessive sleeps or I/O delays.
    - **Solution**:
        - Reduce retry loop timeout in `test_concurrent_logging` to 5 seconds.
        - Minimize sleeps (e.g., 0.05 seconds in tests, 0.2 seconds in `tearDown`).
        - Move logs to a local SSD directory.
4. **PermissionError on Windows**:
    - **Cause**: File locking by other processes (e.g., editors, antivirus).
    - **Solution**:
        - Close all programs accessing `voice_agent_test_logs`.
        - Add retry logic in `_read_log_file` and `tearDown` (already implemented).
5. **QueueListener Stops Early**:
    - **Cause**: Thread termination or queue processing issues.
    - **Solution**:
        - Check `record_count` after flush to verify processed logs.
        - Add debug logging in `CountingQueueListener.handle`:
            
            ```python
            self.logger_instance.logger.debug(f"Processed record for {self.log_type}, total: {self.logger_instance.record_count[self.log_type]}")
            
            ```
            

### Debugging Tips

- **Inspect Files**:
    
    ```powershell
    dir voice_agent_test_logs\metrics
    Get-Content voice_agent_test_logs\metrics\metrics.jsonl | Measure-Object -Line
    Get-Content voice_agent_test_logs\metrics\metrics.jsonl.1 | Measure-Object -Line
    
    ```
    
- **Check Queue Sizes**:
    - Look for `Queue size for ...: {qsize}, file size: {file_size} bytes` in test output.
    - If `qsize > 0` after flush, increase the flush timeout.
- **Monitor Threads**:
    - Check `Active threads after flush: [...]` and `Active threads after close: [...]`.
    - Ensure no lingering `QueueListener` threads.
- **Verify Record Counts**:
    - Check `Record counts after flush: {...}` and `Record counts after retry loop: {...}`.
    - Expected: `metrics: 30`, `conversations: 30`, `errors: 0` for `test_concurrent_logging`.

### Example Debug Output

```
Thread 0 logged 1/10
...
Thread 2 logged 10/10
Active threads after flush: [MainThread, ...]
Record counts after flush: {'conversations': 30, 'metrics': 30, 'errors': 0}
Read 25 logs from voice_agent_test_logs/metrics/metrics.jsonl.1
Read 5 logs from voice_agent_test_logs/metrics/metrics.jsonl0
Total metrics logs: 30
Total conversation logs: 30

```

## Limitations

- **Windows I/O**: File operations may be slower or encounter `PermissionError` issues due to filesystem behavior on Windows. Mitigated with retries and `fsync`.
- **Performance**: The queue-based implementation may introduce delays under high concurrency. Consider async I/O for production scaling.
- **Rotation Overhead**: Small `maxBytes` (e.g., 10000 in tests) leads to frequent rotation, requiring careful test logic to read all files.
- **Memory Usage**: Unlimited queue size (`qsize=-1`) may consume memory under heavy load. A bounded queue could be used in production.

## Future Improvements

- **Async Logging**: Use `asyncio` for non-blocking I/O to improve performance in high concurrency scenarios.
- **Bounded Queues**: Implement bounded queues to limit memory usage in production.
- **Log Aggregation**: Add support for aggregating logs from multiple processes or hosts.
- **Enhanced Monitoring**: Integrate with monitoring tools like Prometheus or ELK for real-time log analysis.