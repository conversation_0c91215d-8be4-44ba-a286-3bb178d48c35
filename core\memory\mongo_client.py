from motor.motor_asyncio import AsyncIOMotorClient
import os

async def get_mongo_client():
    """
    Returns an async MongoDB client using credentials from environment variables.
    Set MONGODB_URI, MONGODB_DB, MONGODB_USERNAME, and MONGODB_PASSWORD in your environment or .env file.
    Example URI: **************************************************
    """
    uri = os.getenv("MONGODB_URI")
    username = os.getenv("MONGODB_USERNAME")
    password = os.getenv("MONGODB_PASSWORD")
    if not uri:
        if username and password:
            uri = f"mongodb://{username}:{password}@localhost:27017/voice_agents?authSource=admin"
        else:
            uri = f"mongodb://localhost:27017/voice_agents"
    client = AsyncIOMotorClient(uri)
    db = client["voice_agents"]
    await db["dialog"].create_index([("user_id", 1)])
    await db["dialog"].create_index([("session_id", 1)])
    await db["dialog"].create_index([("user_id", 1), ("session_id", 1)])

    # Persistent memory collections indexes
    await db["users"].create_index([("user_id", 1)], unique=True)
    await db["call_sessions"].create_index([("session_id", 1)], unique=True)
    await db["call_sessions"].create_index([("user_id", 1)])
    await db["call_sessions"].create_index([("pipeline_id", 1)])
    await db["pipelines"].create_index([("pipeline_id", 1)], unique=True)
    await db["pipelines"].create_index([("user_id", 1)])
    await db["intent_history"].create_index([("intent_id", 1)], unique=True)
    await db["intent_history"].create_index([("user_id", 1)])
    await db["intent_history"].create_index([("session_id", 1)])
    return db

# Example usage:
if __name__ == "__main__":
    db = get_mongo_client()
    print("Collections:", db.list_collection_names()) 