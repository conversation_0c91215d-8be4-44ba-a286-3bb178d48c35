import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = str(Path(__file__).parent.parent.parent)
sys.path.insert(0, project_root)

# Load environment variables BEFORE importing agents
from dotenv import load_dotenv
load_dotenv()

from agents.stt.stt_agent import STTAgent
AUDIO_PATH = "data/filler_words/لحضة واحدة مع حضرتك.mp3"

async def main():
    redis_key="test_session"
    agent = STTAgent(session_id="test_session", state_id="test_state")
    with open(AUDIO_PATH, "rb") as f:
        audio_bytes = f.read()
    session_context = {"language": "ar", "response_format": "text"}
    print(f"Transcribing {AUDIO_PATH}...")
    result = await agent.process(audio_bytes, session_context=session_context)
    print("Result:")
    print(result.model_dump())
    print(await agent.load_context(redis_key),"Context after STT Agent ")

if __name__ == "__main__":
    asyncio.run(main()) 