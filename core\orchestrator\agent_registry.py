import json
from typing import Dict, List, Optional, Any
from agents.base.base_agent import Base<PERSON>gent
from core.memory.redis_context import Redis<PERSON>lient
from schemas.agent_metadata import AgentMetadata
from core.logging.logger import Logger

REGISTRY_REDIS_KEY = "system:agent_registry"

class AgentRegistry:
    """
    A central registry for agents to self-register and be discovered.

    This registry uses Redis hashes for persistent storage of agent metadata and maintains
    an in-memory cache for fast lookups. Each agent's metadata is stored under its name
    in the Redis hash, improving efficiency and scalability.
    """
    def __init__(self, redis_client: Optional[RedisClient] = None):
        self._redis = redis_client
        self._use_redis = redis_client is not None
        self._memory_cache: Dict[str, AgentMetadata] = {}
        self.logger = Logger(log_dir="logs", verbosity="info")
        self.agentObjects: Dict[str, BaseAgent] = {}

    async def initialize(self):
        """
        Initializes the registry. If Redis is configured, it loads all existing
        agent data from Redis into the in-memory cache.
        """
        self.logger.log(
            level="info",
            message="Initializing Agent Registry...",
            session_id="agent_registry",
            state_id="initialize",
            layer="core",
            step="start_initialize",
            custom_module="agent_registry",
            agent_name="AgentRegistry",
            action="initialize",
            input_data=None,
            output_data=None,
            status="running"
        )
        if not self._use_redis:
            self.logger.log(
                level="info",
                message="Registry running in IN-MEMORY mode. No Redis connection.",
                session_id="agent_registry",
                state_id="initialize",
                layer="core",
                step="finish_initialize",
                custom_module="agent_registry",
                agent_name="AgentRegistry",
                action="initialize",
                input_data=None,
                output_data={"mode": "in-memory"},
                status="success"
            )
            return

        try:
            all_agents = await self._redis.client.hgetall(REGISTRY_REDIS_KEY)
            if all_agents:
                for name, json_str in all_agents.items():
                    try:
                        self._memory_cache[name] = AgentMetadata.from_json(json_str)
                    except Exception as e:
                        self.logger.log(
                            level="error",
                            message=f"Failed to load agent '{name}' from Redis: {e}",
                            session_id="agent_registry",
                            state_id="initialize",
                            layer="core",
                            step="load_agent",
                            custom_module="agent_registry",
                            agent_name="AgentRegistry",
                            action="load_from_redis",
                            input_data={"name": name, "json_str": json_str},
                            output_data=None,
                            status="fail"
                        )
                self.logger.log(
                    level="info",
                    message=f"Loaded {len(self._memory_cache)} agents from Redis into cache.",
                    session_id="agent_registry",
                    state_id="initialize",
                    layer="core",
                    step="finish_initialize",
                    custom_module="agent_registry",
                    agent_name="AgentRegistry",
                    action="initialize",
                    input_data=None,
                    output_data={"cached_agents": len(self._memory_cache)},
                    status="success"
                )
            else:
                self.logger.log(
                    level="info",
                    message="No existing agents found in Redis. Starting with an empty registry.",
                    session_id="agent_registry",
                    state_id="initialize",
                    layer="core",
                    step="finish_initialize",
                    custom_module="agent_registry",
                    agent_name="AgentRegistry",
                    action="initialize",
                    input_data=None,
                    output_data={"cached_agents": 0},
                    status="success"
                )
        except Exception as e:
            self.logger.log(
                level="error",
                message=f"Failed to initialize Agent Registry from Redis: {e}",
                session_id="agent_registry",
                state_id="initialize",
                layer="core",
                step="hgetall",
                custom_module="agent_registry",
                agent_name="AgentRegistry",
                action="initialize",
                input_data={"key": REGISTRY_REDIS_KEY},
                output_data=None,
                status="fail"
            )
            raise

    async def register(self, agent_meta: AgentMetadata, agentObject: BaseAgent):
        """
        Registers an agent by adding its metadata to the cache and committing to Redis if configured.
        If an agent with the same name exists, its metadata is updated.
        """
        self.logger.log(
            level="info",
            message=f"Registering agent: {agent_meta.agent_name} (Version: {agent_meta.version})",
            session_id="agent_registry",
            state_id="register",
            layer="core",
            step="start_register",
            custom_module="agent_registry",
            agent_name="AgentRegistry",
            action="register",
            input_data=agent_meta.model_dump(),
            output_data=None,
            status="running"
        )
        try:
            self._memory_cache[agent_meta.agent_name] = agent_meta
            if self._use_redis:
                await self._redis.client.hset(REGISTRY_REDIS_KEY, agent_meta.agent_name, agent_meta.model_dump_json())
            self.logger.log(
                level="info",
                message=f"Agent '{agent_meta.agent_name}' registered successfully.",
                session_id="agent_registry",
                state_id="register",
                layer="core",
                step="finish_register",
                custom_module="agent_registry",
                agent_name="AgentRegistry",
                action="register",
                input_data=agent_meta.model_dump(),
                output_data=None,
                status="success"
            )
            self.agentObjects[agent_meta.agent_name] = agentObject
        except Exception as e:
            self.logger.log(
                level="error",
                message=f"Failed to register agent '{agent_meta.agent_name}': {e}",
                session_id="agent_registry",
                state_id="register",
                layer="core",
                step="hset",
                custom_module="agent_registry",
                agent_name="AgentRegistry",
                action="register",
                input_data=agent_meta.model_dump(),
                output_data=None,
                status="fail"
            )
            raise


    def get(self, agent_name: str) -> Optional[AgentMetadata]:
        """
        Retrieves the metadata for a single agent from the in-memory cache.
        """
        return self._memory_cache.get(agent_name)
    
    def getAgent(self, agent_name: str) -> BaseAgent:
        """
        Retrieves the metadata for a single agent from the in-memory cache.
        """
        return self.agentObjects[agent_name]

    def list_all(self) -> List[AgentMetadata]:
        """
        Returns a list of metadata for all registered agents from the in-memory cache.
        """
        return list(self._memory_cache.values())

"""
Example usage for agent self-registration and pub/sub:

from utils.redis_client import RedisClient
from schemas.agent_metadata import AgentMetadata
from core.agent_registry import AgentRegistry
import asyncio

async def main():
    redis_client = RedisClient()
    registry = AgentRegistry(redis_client)
    await registry.initialize()
    agent_meta = AgentMetadata(
        agent_name="TTSAgent",
        version="1.0",
        description="Handles text-to-speech synthesis",
        entrypoint="agents/tts_agent.py",
        methods=["process"],
        status="healthy",
        capabilities=["synthesis"],
        guard_rails=["output must be audio"]
    )
    await registry.register(agent_meta)
    print(registry.get("TTSAgent"))
    print(registry.list_all())
    # Pub/Sub example
    await redis_client.publish("agent/llm/output", {"text": "Sure, I can help you."})
    async def handle_message(msg):
        print("Received:", msg)
    # To subscribe (in another agent/process):
    # await redis_client.subscribe("agent/llm/output", handle_message)

# asyncio.run(main())
"""
