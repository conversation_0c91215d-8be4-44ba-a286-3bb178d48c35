from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, Optional, List
from enum import Enum
import uuid

class MessageType(str, Enum):
    """Defines the purpose of an A2A message."""
    INSTRUCTION = "instruction"     # A command for an agent/system to do something
    NOTIFICATION = "notification"   # A signal that an event has occurred

class A2AMessage(BaseModel):
    """
    A structured Agent-to-Agent message for communication via the Redis message bus.
    """
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    session_id: str
    message_type: MessageType
    
    source_agent: str = Field(..., description="The agent or system component sending the message.")
    target_agent: str = Field(..., description="The agent or system component intended to receive the message.")
    
    payload: Dict[str, Any] = Field(default_factory=dict, description="A dictionary containing the primary data of the message.")
    context_keys_updated: Optional[List[str]] = Field(None, description="A list of keys in the Redis session context that were updated.")

    def to_json(self) -> str:
        """Serializes the message to a JSON string."""
        return self.model_dump_json()

    @classmethod
    def from_json(cls, json_str: str):
        """Deserializes a JSON string into a message object."""
        return cls.model_validate_json(json_str) 