# Memory Design

This document describes the memory management system of the Voice Agents Platform.

## Overview

The platform uses a hybrid memory approach:

- **Redis**: For real-time session state and inter-agent communication
- **MongoDB**: For persistent conversation history and long-term storage

## Components

### Redis Context
- Session state management
- Real-time message passing
- Temporary data caching

### Persistent MongoDB
- Conversation history
- User preferences
- Analytics data
- Workflow definitions

## Memory Patterns

The memory system follows a layered approach where hot data stays in Redis and cold data is persisted to MongoDB.
