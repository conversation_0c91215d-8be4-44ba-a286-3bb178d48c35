import asyncio
import uuid
from agents.orchestration.orchestrator_agent import Orchestrator
from core.memory.redis_context import RedisClient
from schemas.a2a_message import A2AMessage, MessageType

async def test_orchestrator_communication():
    """Test if the Orchestrator is receiving messages from the agent_completion channel."""
    
    print("=== Testing Orchestrator Communication ===")
    
    # Start the Orchestrator
    orchestrator = Orchestrator(workflow_name="AgentsTry.json")
    orch_task = asyncio.create_task(orchestrator.start())
    await asyncio.sleep(2)  # Give orchestrator time to start
    
    print("Orchestrator started, now sending test message...")
    
    # Create a test message similar to what STT agent would send
    session_id = f"test_debug_{uuid.uuid4().hex[:8]}"
    test_message = A2AMessage(
        session_id=session_id,
        message_type=MessageType.NOTIFICATION,
        source_agent="stt_agent",
        target_agent="Orchestrator",
        payload={
            "status": "complete",
            "output": {
                "status": "success",
                "message": "STT processed successfully",
                "outputs": {
                    "text": "Test transcript",
                    "latencySTT": 1000
                }
            }
        },
        context_keys_updated=["transcript", "latencySTT"]
    )
    
    # Publish the test message to agent_completion channel
    redis = RedisClient()
    print(f"Publishing test message to agent_completion channel...")
    await redis.publish("agent_completion", test_message.to_json())
    
    # Wait a bit to see if orchestrator processes it
    print("Waiting 5 seconds to see if orchestrator processes the message...")
    await asyncio.sleep(5)
    
    # Clean up
    orch_task.cancel()
    try:
        await orch_task
    except asyncio.CancelledError:
        pass
    
    await redis.close()
    print("Test completed.")

if __name__ == "__main__":
    asyncio.run(test_orchestrator_communication())
