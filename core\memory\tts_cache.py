import asyncio
from core.logging.logger_config import get_module_logger
from core.memory.redis_context import RedisClient
from redis.exceptions import RedisError

class TTSCache:
    """
    A cache for TTS (Text-to-Speech) results.
    Uses Redis for storage.
    """
    redis_client = RedisClient()
    
    @staticmethod
    async def generateKey(state: str, text: str, persona: str) -> str:
        """
        Generate a unique key for the TTS result based on session ID, text, persona, and state.
        
        Args:
            session_id (str): The session identifier.
            text (str): The text to be converted to speech.
            persona (str): The persona for the TTS.
            state (str): The state of the TTS process.
        
        Returns:
            str: A unique key for the TTS result.
        """
        return f"{state}:{text}:{persona}"

    @staticmethod
    async def set(key: str, audio_path: str, expires_in: int = 3600) -> None:
        """
        Set the TTS result in the cache.
        
        Args:
            key (str): The unique key for the TTS result.
            audio_path (str): The path to the generated audio file.
        """
        try:
            await TTSCache.redis_client.set(key, audio_path, ex=expires_in)
            logger = get_module_logger("tts_cache")
            logger.info(f"Set TTS cache for key: {key}")
        except Exception as e:  # Catch RedisError or a more specific exception if possible
            logger = get_module_logger("tts_cache")
            logger.warning(f"Redis unavailable, skipping cache set for key {key}: {e}")

    @staticmethod
    async def get(key: str) -> str:
        """
        Get the TTS result from the cache.
        
        Args:
            key (str): The unique key for the TTS result.
        
        Returns:
            str: The path to the cached audio file, or None if not found.
        """
        try:
            audio_path = await TTSCache.redis_client.get(key)
            if audio_path:
                logger = get_module_logger("tts_cache")
                logger.info(f"Retrieved TTS cache for key: {key}")
            return audio_path
        except Exception as e:
            logger = get_module_logger("tts_cache")
            logger.warning(f"Redis unavailable, treating as cache miss for key {key}: {e}")
            return None
        
    @staticmethod
    async def delete(key: str) -> None:
        """
        Delete the TTS result from the cache.
        
        Args:
            key (str): The unique key for the TTS result.
        """
        try:
            await TTSCache.redis_client.delete(key)
            logger = get_module_logger("tts_cache")
            logger.info(f"Deleted TTS cache for key: {key}")
        except Exception as e:
            logger = get_module_logger("tts_cache")
            logger.warning(f"Redis unavailable, skipping cache delete for key {key}: {e}")

    @staticmethod
    async def getExpiryPeriod(key: str) -> int:
        """
        Get the expiry period for a given key in the cache.
        
        Args:
            key (str): The unique key for the TTS result.
        
        Returns:
            int: The expiry period in seconds, or None if not found.
        """
        try:
            expiry = await TTSCache.redis_client.ttl(key)
            return expiry if expiry != -1 else None
        except Exception as e:
            logger = get_module_logger("tts_cache")
            logger.warning(f"Redis unavailable, cannot get expiry for key {key}: {e}")
            return None
