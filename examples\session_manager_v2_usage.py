"""
Session Manager v2 and Orchestrator v3 Usage Example

This example demonstrates how to use the new Session Manager v2 with Orchestrator v3
for complete session lifecycle management with clear architectural ownership.

Key Features Demonstrated:
1. Session creation with proper component initialization
2. Orchestrator v3 integration with dependency injection
3. Session management throughout workflow execution
4. Comprehensive cleanup and resource management
5. Graceful system shutdown
"""

import asyncio
import os
import sys
from typing import Optional

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from core.session_manager_v2 import SessionManagerV2


class VoiceAgentsPlatformExample:
    """
    Example implementation showing how to use Session Manager v2 and Orchestrator v3
    in a real Voice Agents Platform scenario.
    """
    
    def __init__(self, redis_url: Optional[str] = None):
        """
        Initialize the platform example.
        
        Args:
            redis_url: Optional Redis URL (defaults to localhost)
        """
        self.session_manager = SessionManagerV2(redis_url=redis_url)
        self.active_sessions = {}
    
    async def start_voice_session(self, workflow_name: str, user_id: str = None) -> str:
        """
        Start a new voice session with complete lifecycle management.
        
        Args:
            workflow_name: Name of the workflow to execute (e.g., "filler_words_detection")
            user_id: Optional user identifier
            
        Returns:
            str: Session ID for the created session
        """
        print(f"🚀 Starting new voice session with workflow: {workflow_name}")
        
        try:
            # Step 1: Create session with all components initialized
            session_id = await self.session_manager.create_session(
                workflow_name=workflow_name,
                user_id=user_id
            )
            
            print(f"✅ Session created: {session_id}")
            
            # Step 2: Initialize Orchestrator v3 with dependency injection
            orchestrator = await self.session_manager.initialize_orchestrator(session_id)
            
            print(f"✅ Orchestrator v3 initialized for session: {session_id}")
            
            # Store session reference
            self.active_sessions[session_id] = {
                "workflow_name": workflow_name,
                "user_id": user_id,
                "orchestrator": orchestrator
            }
            
            return session_id
            
        except Exception as e:
            print(f"❌ Failed to start voice session: {e}")
            raise
    
    async def run_workflow(self, session_id: str, workflow_name: Optional[str] = None):
        """
        Run the workflow for a session using Orchestrator v3.
        
        Args:
            session_id: Session ID to run workflow for
            workflow_name: Optional workflow name override
        """
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
        
        session_info = self.active_sessions[session_id]
        orchestrator = session_info["orchestrator"]
        
        print(f"🔄 Running workflow for session: {session_id}")
        
        try:
            # Start the orchestrator with optional workflow override
            await orchestrator.start(workflow_name=workflow_name)
            
            print(f"✅ Workflow completed for session: {session_id}")
            
        except Exception as e:
            print(f"❌ Workflow execution failed for session {session_id}: {e}")
            raise
    
    async def save_session_data(self, session_id: str) -> bool:
        """
        Save session dialog and data throughout the session lifecycle.
        
        Args:
            session_id: Session ID to save data for
            
        Returns:
            bool: True if successful, False otherwise
        """
        print(f"💾 Saving session data for: {session_id}")
        
        try:
            result = await self.session_manager.save_dialog_log(session_id)
            
            if result:
                print(f"✅ Session data saved for: {session_id}")
            else:
                print(f"⚠️ Failed to save session data for: {session_id}")
            
            return result
            
        except Exception as e:
            print(f"❌ Error saving session data for {session_id}: {e}")
            return False
    
    async def end_session(self, session_id: str, reason: str = "session_complete"):
        """
        End a session with comprehensive cleanup.
        
        Args:
            session_id: Session ID to end
            reason: Reason for ending the session
        """
        print(f"🛑 Ending session: {session_id} (reason: {reason})")
        
        try:
            # Save any remaining data before cleanup
            await self.save_session_data(session_id)
            
            # Perform comprehensive cleanup
            result = await self.session_manager.cleanup_session(session_id, reason)
            
            if result:
                # Remove from our tracking
                if session_id in self.active_sessions:
                    del self.active_sessions[session_id]
                
                print(f"✅ Session ended successfully: {session_id}")
            else:
                print(f"⚠️ Session cleanup had issues: {session_id}")
            
        except Exception as e:
            print(f"❌ Error ending session {session_id}: {e}")
            raise
    
    async def shutdown_platform(self):
        """
        Gracefully shutdown the entire platform.
        """
        print("🔄 Shutting down Voice Agents Platform...")
        
        try:
            # End all active sessions
            session_ids = list(self.active_sessions.keys())
            for session_id in session_ids:
                await self.end_session(session_id, "platform_shutdown")
            
            # Shutdown session manager
            await self.session_manager.shutdown()
            
            print("✅ Platform shutdown completed successfully")
            
        except Exception as e:
            print(f"❌ Error during platform shutdown: {e}")
            raise
    
    def get_session_status(self, session_id: str) -> dict:
        """
        Get status information for a session.
        
        Args:
            session_id: Session ID to get status for
            
        Returns:
            dict: Session status information
        """
        # Get info from session manager
        session_info = self.session_manager.get_session_info(session_id)
        
        if not session_info:
            return {"status": "not_found"}
        
        # Add orchestrator status if available
        if session_id in self.active_sessions:
            orchestrator = self.active_sessions[session_id]["orchestrator"]
            orchestrator_status = orchestrator.get_status()
            session_info.update(orchestrator_status)
        
        return session_info
    
    def list_active_sessions(self) -> list:
        """
        List all active sessions.
        
        Returns:
            list: List of active session IDs
        """
        return self.session_manager.list_active_sessions()


async def main():
    """
    Main example demonstrating Session Manager v2 and Orchestrator v3 usage.
    """
    print("🎯 Voice Agents Platform - Session Manager v2 & Orchestrator v3 Example\n")
    
    # Initialize platform
    platform = VoiceAgentsPlatformExample()
    
    try:
        # Example 1: Single session workflow
        print("📋 Example 1: Single Session Workflow")
        session_id = await platform.start_voice_session("filler_words_detection", "user_123")
        
        # Check session status
        status = platform.get_session_status(session_id)
        print(f"📊 Session status: {status['status']}, workflow: {status['workflow_name']}")
        
        # Save session data periodically
        await platform.save_session_data(session_id)
        
        # End session
        await platform.end_session(session_id)
        
        print("\n" + "="*60 + "\n")
        
        # Example 2: Multiple concurrent sessions
        print("📋 Example 2: Multiple Concurrent Sessions")
        
        sessions = []
        workflows = ["filler_words_detection", "sentiment_analysis", "intent_classification"]
        
        # Start multiple sessions
        for i, workflow in enumerate(workflows):
            session_id = await platform.start_voice_session(workflow, f"user_{i+1}")
            sessions.append(session_id)
        
        # List active sessions
        active_sessions = platform.list_active_sessions()
        print(f"📊 Active sessions: {len(active_sessions)}")
        
        # Save data for all sessions
        for session_id in sessions:
            await platform.save_session_data(session_id)
        
        # End all sessions
        for session_id in sessions:
            await platform.end_session(session_id)
        
        print("\n" + "="*60 + "\n")
        
        # Example 3: Error handling
        print("📋 Example 3: Error Handling")
        
        # Try to get status of non-existent session
        status = platform.get_session_status("non_existent_session")
        print(f"📊 Non-existent session status: {status['status']}")
        
        # Try to save data for non-existent session
        result = await platform.save_session_data("non_existent_session")
        print(f"💾 Save result for non-existent session: {result}")
        
        print("\n" + "="*60 + "\n")
        
        print("🎉 All examples completed successfully!")
        
    except Exception as e:
        print(f"❌ Example failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Always shutdown gracefully
        await platform.shutdown_platform()


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())
