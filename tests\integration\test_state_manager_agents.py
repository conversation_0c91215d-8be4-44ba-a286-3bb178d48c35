import sys
import asyncio
import os
import time
from pathlib import Path
from dotenv import load_dotenv
# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

load_dotenv()

from core.state_manager.state_manager import StateManager
from core.logging.logger_config import setup_development_logging, get_module_logger, cleanup_logger
from core.memory.redis_context import RedisClient
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType

logger = get_module_logger("test_state_manager_agents", session_id="test_state_manager_agents")


async def run_trial(sessionId: str = "test_session", userId: str = "user_1"):
    """
    Runs a simple trial of the StateManager with the AgentsTry workflow.
    """
    try: 
        sm = await StateManager.create("AgentsTry.json", sessionId, userId)
        
        # Step 1: Simulate greeting state
        greeting_input = {"audio_path": os.path.join("fillerWords", "لحضة واحدة مع حضرتك.mp3")}
        greeting_result = await sm.execute_step(greeting_input)
        print("Greeting Result:", greeting_result)
        
        # Step 2: Simulate state 2
        await sm.transition("state_2")
        state_2_input = {"audio_path": "fillerWords\لحضة واحدة مع حضرتك.mp3"}
        state_2_result = await sm.execute_step(state_2_input)
        print("State 2 Result:", state_2_result)

        # Step 3: Simulate state 3
        await sm.transition("state_3")
        state_3_input = {"audio_path": "fillerWords\لحضة واحدة مع حضرتك.mp3"}
        state_3_result = await sm.execute_step(state_3_input)
        print("State 3 Result:", state_3_result)


    except Exception as e:
        logger.error(f"Error during trial: {e}")
        return StateOutput(
            status=StatusType.ERROR,
            message=str(e),
            code=StatusCode.INTERNAL_ERROR,
            outputs={},
            meta={"source": "test_suite"}
        )



if __name__ == "__main__":
    try:
        asyncio.run(run_trial(sessionId="test_session", userId="user_1"))
    except Exception as e:
        logger.error(
            "Error in Voice Agents Platform demo",
            action="main",
            reason=str(e),
            layer="demo"
        )
        raise
    finally:
        # Clean up logger resources
        cleanup_logger()
   