import asyncio
import uuid
import logging
from typing import Dict, Any
import json

from core.memory.redis_context import RedisClient
from schemas.a2a_message import A2AMessage, MessageType

# Basic logging setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

AGENT_NOTIFICATION_CHANNEL = "agent_notifications"

def get_session_key(session_id: str) -> str:
    """Generates the Redis key for a given session's context."""
    return f"session:{session_id}:context"

async def agent_task(agent_name: str, session_id: str, process_logic: callable):
    """A wrapper for an agent that subscribes to its instruction channel and runs logic."""
    redis = RedisClient() # add url="redis://localhost:9999" to test fallback mechanism
    instruction_channel = f"agent:{agent_name}:{session_id}"
    logger.info(f"[{agent_name}] Started. Listening on '{instruction_channel}'")
    
    async def callback(message_json: str):
        message = A2AMessage.from_json(message_json)
        logger.info(f"[{agent_name}] Received instruction: {message.payload}")
        await process_logic(redis, session_id, message.payload)
        # In a real system, the agent would continue listening.
        # For this example, we'll stop after one instruction.
        raise asyncio.CancelledError 

    try:
        await redis.subscribe(instruction_channel, callback)
    except asyncio.CancelledError:
        logger.info(f"[{agent_name}] Task complete, shutting down listener.")
    finally:
        await redis.close()


async def stt_logic(redis: RedisClient, session_id: str, payload: Dict):
    """The core logic for the STT agent."""
    context_key = get_session_key(session_id)
    context = await redis.get(context_key) or {}
    transcript = "Hello, I want to know my account balance."
    context.update({"transcript": transcript})
    await redis.set(context_key, context, ex=3600)
    
    notification = A2AMessage(
        session_id=session_id,
        message_type=MessageType.NOTIFICATION,
        source_agent="STTAgent",
        target_agent="Orchestrator",
        payload={"status": "complete"},
        context_keys_updated=["transcript"]
    )
    await redis.publish(AGENT_NOTIFICATION_CHANNEL, notification.to_json())


async def intent_parser_logic(redis: RedisClient, session_id: str, payload: Dict):
    """The core logic for the Intent Parser agent."""
    context_key = get_session_key(session_id)
    context = await redis.get(context_key)
    if not context or "transcript" not in context:
        logger.warning("[Intent Parser] No transcript in context to process.")
        return
    logger.info(f"[Intent Parser] Read context: {context}")

    # 2. Modify context based on transcript
    transcript = context.get("transcript", "").lower()
    intent = "unknown"
    if "balance" in transcript:
        intent = "query_balance"
    elif "loan" in transcript:
        intent = "loan_inquiry"

    context.update({
        "intent": intent,
        "intent_parser_timestamp": asyncio.get_event_loop().time()
    })

    # 3. Write context back
    await redis.set(context_key, context, ex=3600)
    logger.info(f"[Intent Parser] Wrote updated context: {context}")
    
    notification = A2AMessage(
        session_id=session_id,
        message_type=MessageType.NOTIFICATION,
        source_agent="IntentParserAgent",
        target_agent="Orchestrator",
        payload={"status": "done", "intent": intent},
        context_keys_updated=["intent", "intent_parser_timestamp"]
    )
    await redis.publish(AGENT_NOTIFICATION_CHANNEL, notification.to_json())


async def llm_responder_logic(redis: RedisClient, session_id: str, payload: Dict):
    """The core logic for the LLM Responder agent."""
    context_key = get_session_key(session_id)
    context = await redis.get(context_key)
    if not context or "intent" not in context:
        logger.warning("[LLM Responder] No intent in context to process.")
        return
    logger.info(f"[LLM Responder] Read context: {context}")

    intent = context.get("intent")
    response_text = "I'm sorry, I don't understand."
    if intent == "query_balance":
        response_text = "Your current account balance is $1,234.56."
    elif intent == "loan_inquiry":
        response_text = "We have several loan products available. Would you like to hear more?"

    context.update({
        "final_response": response_text,
        "llm_timestamp": asyncio.get_event_loop().time()
    })

    await redis.set(context_key, context, ex=3600)
    logger.info(f"[LLM Responder] Wrote updated context: {context}")
    
    notification = A2AMessage(
        session_id=session_id,
        message_type=MessageType.NOTIFICATION,
        source_agent="LLMResponderAgent",
        target_agent="Orchestrator",
        payload={"status": "done"},
        context_keys_updated=["final_response", "llm_timestamp"]
    )
    await redis.publish(AGENT_NOTIFICATION_CHANNEL, notification.to_json())


async def tts_logic(redis: RedisClient, session_id: str, payload: Dict):
    """The core logic for the TTS agent."""
    context_key = get_session_key(session_id)
    context = await redis.get(context_key)
    if not context or "final_response" not in context:
        logger.warning("[TTS Agent] No final_response in context to process.")
        return
    logger.info(f"[TTS Agent] Read context: {context}")

    response_text = context.get("final_response")
    audio_url = f"s3://voice-agent-audio/{session_id}-{uuid.uuid4().hex[:8]}.wav"

    context.update({
        "audio_url": audio_url,
        "tts_timestamp": asyncio.get_event_loop().time()
    })

    await redis.set(context_key, context, ex=3600)
    logger.info(f"[TTS Agent] Wrote updated context: {context}")
    
    notification = A2AMessage(
        session_id=session_id,
        message_type=MessageType.NOTIFICATION,
        source_agent="TTSAgent",
        target_agent="Orchestrator",
        payload={"status": "done", "audio_url": audio_url},
        context_keys_updated=["audio_url", "tts_timestamp"]
    )
    await redis.publish(AGENT_NOTIFICATION_CHANNEL, notification.to_json())


async def orchestrator(redis: RedisClient, session_id: str):
    """Listens for notifications and sends instructions to the next agent."""
    logger.info("[Orchestrator] Started, listening for notifications...")

    async def callback(message_json: str):
        message = A2AMessage.from_json(message_json)
        if message.session_id != session_id or message.target_agent != "Orchestrator":
            return

        from_agent = message.source_agent
        logger.info(f"[Orchestrator] Received notification from: {from_agent}")

        next_agent = None
        if from_agent == "STTAgent":
            next_agent = "IntentParserAgent"
        elif from_agent == "IntentParserAgent":
            next_agent = "LLMResponderAgent"
        elif from_agent == "LLMResponderAgent":
            next_agent = "TTSAgent"
        elif from_agent == "TTSAgent":
            logger.info("[Orchestrator] Full workflow complete.")
            raise asyncio.CancelledError

        if next_agent:
            instruction = A2AMessage(
                session_id=session_id,
                message_type=MessageType.INSTRUCTION,
                source_agent="Orchestrator",
                target_agent=next_agent,
                payload={"action": "process"}
            )
            instruction_channel = f"agent:{next_agent}:{session_id}"
            logger.info(f"[Orchestrator] Sending instruction to {next_agent} on {instruction_channel}")
            await redis.publish(instruction_channel, instruction.to_json())
        else:
            logger.info("[Orchestrator] Full workflow complete.")
            raise asyncio.CancelledError

    try:
        await redis.subscribe(AGENT_NOTIFICATION_CHANNEL, callback)
    except asyncio.CancelledError:
        logger.info("[Orchestrator] Workflow finished, shutting down listener.")


async def main():
    """Main function to run the simulation."""
    redis_client = RedisClient()
    session_id = f"session_{uuid.uuid4().hex[:8]}"
    
    logger.info(f"--- Starting A2A simulation for session: {session_id} ---")

    # Create tasks for all agents and the orchestrator
    stt = asyncio.create_task(agent_task("STTAgent", session_id, stt_logic))
    intent_parser = asyncio.create_task(agent_task("IntentParserAgent", session_id, intent_parser_logic))
    llm_responder = asyncio.create_task(agent_task("LLMResponderAgent", session_id, llm_responder_logic))
    tts = asyncio.create_task(agent_task("TTSAgent", session_id, tts_logic))
    orch = asyncio.create_task(orchestrator(redis_client, session_id))
    
    await asyncio.sleep(0.1) # Let subscribers connect

    # Kick off the entire process by sending the first instruction
    first_instruction = A2AMessage(
        session_id=session_id,
        message_type=MessageType.INSTRUCTION,
        source_agent="System",
        target_agent="STTAgent",
        payload={"action": "process_audio", "source": "mic"}
    )
    await redis_client.publish(f"agent:STTAgent:{session_id}", first_instruction.to_json())

    # Wait for the orchestrator to complete the flow
    await orch
    
    # Verify the final context
    final_context = await redis_client.get(get_session_key(session_id))
    logger.info("--- Final context in Redis ---")
    logger.info(json.dumps(final_context, indent=2))
    
    # Clean up any remaining agent tasks
    stt.cancel()
    intent_parser.cancel()
    llm_responder.cancel()
    tts.cancel()

    logger.info("--- Simulation finished ---")
    await redis_client.close()


if __name__ == "__main__":
    # To run this example:
    # 1. Make sure your Redis container is running: `docker-compose up -d`
    # 2. Run this script from the root of your project: `python -m scripts.redis_usage_example`
    asyncio.run(main())