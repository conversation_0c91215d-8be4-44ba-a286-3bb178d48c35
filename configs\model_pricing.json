{"gpt-4o-mini": {"input": 0.00015, "output": 0.0006, "provider": "openai", "type": "text", "description": "OpenAI GPT-4o Mini - Most cost-effective model"}, "gpt-4o": {"input": 0.005, "output": 0.015, "provider": "openai", "type": "text", "description": "OpenAI GPT-4o - Latest flagship model"}, "gpt-4-turbo": {"input": 0.01, "output": 0.03, "provider": "openai", "type": "text", "description": "OpenAI GPT-4 Turbo - Fast and capable"}, "claude-3-opus": {"input": 0.015, "output": 0.075, "provider": "anthropic", "type": "text", "description": "Anthropic Claude 3 Opus - Most capable model"}, "claude-3-sonnet": {"input": 0.003, "output": 0.015, "provider": "anthropic", "type": "text", "description": "Anthropic Claude 3 Sonnet - Balanced performance"}, "claude-3-haiku": {"input": 0.00025, "output": 0.00125, "provider": "anthropic", "type": "text", "description": "Anthropic Claude 3 Haiku - Fast and affordable"}, "gemini-1.5-pro": {"input": 0.0035, "output": 0.0105, "provider": "google", "type": "text", "description": "Google Gemini 1.5 Pro - Long context model"}, "gemini-1.5-flash": {"input": 7.5e-05, "output": 0.0003, "provider": "google", "type": "text", "description": "Google Gemini 1.5 Flash - Fast and efficient"}, "whisper-1": {"cost_per_minute": 0.006, "provider": "openai", "type": "stt", "description": "OpenAI Whisper - Speech to text"}, "tts-1": {"cost_per_1k_chars": 0.015, "provider": "openai", "type": "tts", "description": "OpenAI TTS - Text to speech"}, "tts-1-hd": {"cost_per_1k_chars": 0.03, "provider": "openai", "type": "tts", "description": "OpenAI TTS HD - High quality text to speech"}, "elevenlabs": {"cost_per_1k_chars": 0.18, "provider": "elevenlabs", "type": "tts", "description": "ElevenLabs - Premium voice synthesis"}}