import json
import sys
from pathlib import Path
from typing import Optional, Dict, Set, Union
from pydantic import ValidationError
import aiofiles

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from schemas.workflow_schema import Layer2, PipelineStep
from core.logging.exceptions import SchemaError, Layer2Error
from core.logging.logger_config import get_module_logger
import asyncio

logger = get_module_logger("layer2_schema_loader")


class Layer2SchemaLoader:
    @staticmethod
    async def load(file_path: str, stateExpectedOutputs: list[str]) -> Optional[Layer2]:
        """
        Loads a Layer 2 schema JSON file and parses it into a Layer2 object.

        Args:
            file_path (str): Path to the Layer 2 JSON file.

        Returns:
            Layer2: Parsed Layer2 object, or None if there's an error.
            
        Raises:
            SchemaError: If the JSON schema is invalid
            Layer2Error: If there are logical errors in the Layer2 definition
        """
        try:
            async with aiofiles.open(file_path, 'r') as f:
                data = json.loads(await f.read())
                # Only validate at input boundary
                layer2 = Layer2.model_validate(data)
                
                # Keep custom validation for pipeline connections
                await Layer2SchemaLoader._validate_pipeline_connections(layer2, stateExpectedOutputs)
                
                logger.info(
                    "Successfully loaded workflow schema",
                    action="load_workflow",
                    output_data={"file": file_path, "layer2_id": layer2.id},
                    layer="schema_loading"
                )
                return layer2
        except FileNotFoundError:
            logger.error(
                "File not found",
                action="load_workflow",
                input_data={"file": file_path},
                reason="File not found",
                layer="schema_loading"
            )
            raise SchemaError(f"Layer2 schema file not found: {file_path}")
        except json.JSONDecodeError as e:
            logger.error(
                "Invalid JSON format",
                action="load_workflow",
                input_data={"file": file_path, "error": str(e)},
                reason=str(e),
                layer="schema_loading"
            )
            raise SchemaError(f"Invalid JSON format in Layer2 schema: {e}")
        except ValidationError as e:
            logger.error(
                "JSON does not match the expected schema",
                action="load_workflow",
                input_data={"file": file_path, "error": str(e)},
                reason=str(e),
                layer="schema_loading"
            )
            raise SchemaError(f"Layer2 schema validation failed: {e}")
    
    @staticmethod
    async def _validate_pipeline_connections(layer2: Layer2, stateExpectedOutputs: list[str]) -> None:
        """
        Validates that pipeline steps correctly reference outputs from previous steps.
        
        Args:
            layer2: The Layer2 object to validate
            
        Raises:
            Exception: If any pipeline step references an undefined output
        """
        # Track all available outputs from previous steps
        available_outputs: Dict[str, Set[str]] = {}
        
        for i, step in enumerate(layer2.pipeline):
            # Check input references (skip for the first step)
            if i > 0:  # Only validate inputs for non-first steps
                for input_key, input_value in step.input.items():
                    # Skip if the input is a template variable (e.g., {{slots.account_id}})
                    if isinstance(input_value, str) and not input_value.startswith("{{"):
                        # Check if the referenced output exists
                        if input_value not in available_outputs:
                            raise Exception(
                                f"Pipeline step '{step.step}' references undefined output '{input_value}' "
                                f"in Layer2 '{layer2.id}'"
                            )
            
            # Add this step's outputs to available outputs
            for output_key, output_value in step.output.items():
                if output_value not in available_outputs:
                    available_outputs[output_value] = set()
                available_outputs[output_value].add(step.step)
        
        for output in stateExpectedOutputs:
            if output not in available_outputs:
                raise Exception(
                    f"Expected output '{output}' is not produced by any pipeline step in Layer2 '{layer2.id}'"
                )