# Voice-Agents-Platform

A modular, scalable voice AI platform with Redis pub/sub architecture where agents subscribe to Redis, STT agent processes audio input and publishes results, and Orchestrator Agent manages state transitions in a cycle until next user prompt.

## 🏗️ Architecture Overview

The platform follows a **clean, modular architecture** designed for scalability, maintainability, and low-latency execution with the following key principles:

- **Separation of Concerns**: Each component has a single responsibility
- **Redis Pub/Sub Communication**: Agents communicate through Redis for real-time messaging
- **MongoDB Persistence**: Long-term storage for conversations and analytics
- **Structured Logging**: Comprehensive logging for debugging and monitoring
- **Type Safety**: Pydantic schemas for data validation

---

## 📁 Repository Structure

### 🤖 `agents/` — AI Agent Components

Organized by functionality with specialized agents for different tasks:

```
agents/
├── base/                    # Abstract base classes and shared logic
│   └── base_agent.py       # Core agent interface with logging and context
├── stt/                    # Speech-to-Text agents
│   └── stt_agent.py       # OpenAI Whisper integration
├── tts/                    # Text-to-Speech agents
│   ├── tts_agent.py       # Generic TTS interface
│   ├── tts_openai.py      # OpenAI TTS implementation
│   └── tts_elevenlabs.py  # ElevenLabs TTS implementation
├── processing/             # Text processing and analysis
│   ├── preprocessing_agent.py    # Text cleaning and normalization
│   ├── processing_agent.py       # Business logic and LLM integration
│   ├── intent_classifier.py      # Intent detection
│   ├── disambiguator.py          # Language disambiguation
│   └── emotion_classifier.py     # Emotion analysis
├── orchestration/          # Workflow management
│   └── orchestrator_agent.py    # Main workflow orchestrator
└── filler/                 # Audio filler management
    └── filler_tts_agent.py      # Filler audio generation
```

### 🏗️ `core/` — Platform Runtime Logic

The core infrastructure handling runtime mechanics, session management, state control, and system orchestration:

```
core/
├── main.py                 # Application entry point
├── session/               # Session lifecycle management
│   ├── session_manager.py       # Main session handler
│   ├── session_context.py       # Runtime context object
│   └── session_utils.py         # Session utilities
├── state_manager/         # State control and transitions
│   ├── state_manager.py         # Main state manager
│   ├── state_output.py          # State output handling
│   ├── layer2_pipeline.py       # Pipeline execution logic
│   ├── state.py                 # State definitions
│   └── transitions.py           # State transition logic
├── orchestrator/          # Workflow execution engine
│   ├── orchestrator.py          # Workflow orchestration
│   └── agent_registry.py        # Agent registration system
├── memory/               # Data persistence and caching
│   ├── memory_manager.py        # Multi-layer memory management
│   ├── redis_context.py         # Redis integration
│   ├── persistent_mongo.py      # MongoDB persistence
│   ├── mongo_client.py          # MongoDB client
│   ├── mongo_retrieval.py       # Data retrieval utilities
│   └── persistent_mongo_schema.py # MongoDB schemas
└── logging/              # Centralized logging system
    ├── logger.py                # Core logger implementation
    ├── logger_config.py         # Logger configuration
    ├── middleware.py            # Logging middleware
    └── exceptions.py            # Custom exceptions
### ⚙️ `configs/` — Configuration Management

All configuration files for different system components:

```
configs/
├── .env.example                    # Environment variables template
├── docker-compose.yml              # Docker orchestration
├── environment.yml                 # Conda environment definition
├── state_manager_config.json       # State manager configuration
├── l2_greeting.json                # Layer2 greeting pipeline
├── l2_goodbye.json                 # Layer2 goodbye pipeline
├── l2_check_balance.json           # Layer2 balance check pipeline
├── l2_fallback.json                # Layer2 fallback pipeline
├── l2_ai_greeting.json             # AI greeting configuration
├── l2_state_2.json                 # Layer2 state configuration
├── l2_state_3.json                 # Layer2 state configuration
└── l2_try_agents.json              # Layer2 agent testing configuration
```

### 📊 `schemas/` — Data Models & Validation

Pydantic models for data validation and structure:

```
schemas/
├── __init__.py                     # Package initialization
├── a2a_message.py                  # Agent-to-Agent message schema
├── agent_metadata.py               # Agent metadata for registration
├── outputSchema.py                 # Output schema definitions
├── layer2_schema.py                # Layer2 pipeline schemas
├── workflow_schema.py              # Workflow configuration schemas
└── mongo_schema.py                 # MongoDB schema definitions
```

### 📁 `data/` — Assets and Resources

Audio assets, training data, and resource files:

```
data/
├── filler_words/                   # Pre-recorded Arabic audio fillers
├── audio_samples/                  # Sample audio files for testing
└── prompts/                        # LLM prompt templates
```

### 🧪 `tests/` — Test Suite

Comprehensive testing framework:

```
tests/
├── unit/                          # Unit tests for individual components
├── integration/                   # Integration tests for component interaction
└── e2e/                          # End-to-end system tests
```

### 🔄 `workflows/` — Workflow Definitions

JSON configuration files defining conversation workflows:

```
workflows/
├── workflow_source_of_truth_generic_banking_workflow.json
└── workflow_source_of_truth_stt_test_workflow.json
```

### 📜 `scripts/` — Utilities and Testing

CLI utilities, testing scripts, and development tools:

```
scripts/
├── test_pipeline_runner.py        # Pipeline testing utility
├── comprehensive_system_test.py   # Full system validation
├── test_stt_agent_mp3.py         # STT agent testing
├── test_preprocessing_agent.py    # Preprocessing agent testing
├── test_processing_agent.py       # Processing agent testing
├── test_tts_agent.py              # TTS agent testing
├── test_filler_tts_agent.py       # Filler agent testing
├── full_system_integration_test.py # Integration testing
├── orchestrator_inprocess_test.py  # Orchestrator testing
└── debug_orchestrator_communication.py # Debug utilities
```

### 🛠️ `utils/` — Shared Utilities

Common helper functions and utilities:

```
utils/
├── audio_utils.py                 # Audio processing utilities
├── language_utils.py              # Language detection and processing
└── validators.py                  # Input validation and security
```

### 🔒 `secrets/` — Sensitive Configuration

Non-versioned sensitive files (gitignored):

```
secrets/
└── google_secret.json            # Google service account credentials
```

### 🌐 `frontend/` — User Interface

Web-based visualization and debugging tools:

```
frontend/
└── README.md                     # Frontend documentation
```

### 📚 `docs/` — Documentation

Comprehensive project documentation:

```
docs/
├── architecture.md               # System architecture overview
├── memory_design.md              # Memory management design
├── agent_protocols.md            # Agent communication protocols
├── state_output_guide.md         # State output handling guide
└── LOGGING_INTEGRATION_GUIDE.md  # Logging system integration
---

## 🚀 Getting Started

### Prerequisites

- Python 3.11+
- Redis server
- MongoDB server
- OpenAI API key
- ElevenLabs API key (optional)

### Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/Sahla-Smart-Solutions/Voice-Agents-Platform.git
   cd Voice-Agents-Platform
   ```

2. **Set up the environment:**
   ```bash
   conda env create -f configs/environment.yml
   conda activate voice-agents-platform
   ```

3. **Configure environment variables:**
   ```bash
   cp configs/.env.example .env
   # Edit .env with your API keys and configuration
   ```

4. **Start required services:**
   ```bash
   # Start Redis and MongoDB
   docker-compose -f configs/docker-compose.yml up -d
   ```

### Running the System

1. **Start the main application:**
   ```bash
   python core/main.py
   ```

2. **Run comprehensive tests:**
   ```bash
   python scripts/comprehensive_system_test.py
   ```

3. **Test individual components:**
   ```bash
   # Test STT agent
   python scripts/test_stt_agent_mp3.py

   # Test TTS agent
   python scripts/test_tts_agent.py

   # Test full pipeline
   python scripts/test_pipeline_runner.py
   ```

### Development

- **Run unit tests:** Individual component testing in `tests/unit/`
- **Run integration tests:** Component interaction testing in `tests/integration/`
- **Debug with scripts:** Use utilities in `scripts/` for debugging specific components

### Architecture Validation

The system has been successfully reorganized and validated with:
- ✅ All major components can be imported
- ✅ Logging system functions correctly
- ✅ Main entry point runs successfully
- ✅ File structure matches design specifications

For detailed architecture information, see `docs/architecture.md`.
