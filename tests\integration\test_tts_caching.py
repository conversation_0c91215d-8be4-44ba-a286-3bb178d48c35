import sys
import asyncio
import os
import time
from pathlib import Path
from dotenv import load_dotenv
# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

load_dotenv()

from core.state_manager.state_manager import StateManager
from core.logging.logger_config import setup_development_logging, get_module_logger, cleanup_logger
from core.memory.redis_context import RedisClient
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType

logger = get_module_logger("test_state_manager_agents_v2", session_id="test_state_manager_agents_v2")

async def run_trial(sessionId: str = "test_session_2", userId: str = "user_1"):
    try:
        sm = await StateManager.create("banking_workflow_v2.json", sessionId, userId)
        
        # test 1 - test tts cache
        await sm.transitionWorkflow("CheckBalance")
        await sm.transitionPipeline("filler_tts")
        tts_input = {"filler_text": "hello from filler text first version", "emotion": "default", "gender": "female", "expires_in": 60}
        # await sm.transitionPipeline("tts")
        # tts_input = {"text": "hello from tts third version", "emotion": "default", "gender": "female", "expires_in": 150}
        tts_result = await sm.executePipelineState(tts_input)
        print("TTS Result: ", tts_result)


    except Exception as e:
        logger.error(f"Error during trial setup: {e}")
        return StateOutput(
            status=StatusType.ERROR,
            message=str(e),
            code=StatusCode.INTERNAL_ERROR,
            outputs={},
            meta={"source": "test_suite"}
        )
    
if __name__ == "__main__":
    try:
        setup_development_logging()
        asyncio.run(run_trial())
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
    finally:
        cleanup_logger()  # Ensure logger is cleaned up properly

