import asyncio
from core.state_manager.state_manager import StateManager

async def test_workflow_summary_persistence():
    # Use test session and user ids
    session_id = "test_session_001"
    user_id = "test_user_001"
    workflow_name = "GenericBank.json"

    # Create and initialize the StateManager
    state_manager = await StateManager.create(workflow_name, session_id, user_id)

    # Retrieve the workflow summary from persistent memory
    workflow_id = state_manager.workflow.workflow.id
    summary = await state_manager.memory_manager.get(f"workflow_source_of_truth_{workflow_id}")

    print("=== Workflow Summary from Persistent Memory ===")
    if summary:
        import json
        print(json.dumps(summary, indent=2, ensure_ascii=False))
    else:
        print("No summary found in persistent memory.")

if __name__ == "__main__":
    asyncio.run(test_workflow_summary_persistence()) 