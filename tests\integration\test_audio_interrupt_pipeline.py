import asyncio
import os
from dotenv import load_dotenv
from core.state_manager.state_manager import StateManager
load_dotenv()
async def select_input_device():
    import sounddevice as sd
    devices = sd.query_devices()
    input_devices = [(i, d) for i, d in enumerate(devices) if d['max_input_channels'] > 0]
    print("Available input devices:")
    for idx, dev in input_devices:
        print(f"  [{idx}] {dev['name']} (inputs: {dev['max_input_channels']})")
    while True:
        try:
            device_index = int(input("Enter the device index for your microphone: ").strip())
            if any(idx == device_index for idx, _ in input_devices):
                return device_index
            else:
                print("Invalid index. Please select from the list above.")
        except Exception:
            print("Please enter a valid integer index.")

async def record_audio_wav(duration_sec=3, sample_rate=16000, device_index=None):
    import sounddevice as sd
    import numpy as np
    import wave
    print(f"[MIC] Recording {duration_sec} seconds of audio...")
    audio = sd.rec(int(duration_sec * sample_rate), samplerate=sample_rate, channels=1, dtype='int16', device=device_index)
    sd.wait()
    audio_bytes = audio.tobytes()
    with wave.open('last_recorded.wav', 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(sample_rate)
        wf.writeframes(audio_bytes)
    print(f"[INFO] Saved your recorded audio to: last_recorded.wav")
    return 'last_recorded.wav'

async def run_audio_interrupt_pipeline():
    device_index = None
    print("Choose input method:")
    print("1. Record audio from microphone")
    print("2. Provide path to audio file")
    choice = input("Enter your choice (1 or 2): ").strip()
    if choice == "1":
        device_index = await select_input_device()
        audio_path = await record_audio_wav(device_index=device_index)
    elif choice == "2":
        path = input("Enter path to audio file (.wav or .mp3): ").strip()
        if not os.path.exists(path):
            print("File not found.")
            return
        audio_path = path
    else:
        print("Invalid choice.")
        return

    try:
        workflow_name = "banking_workflow.json"
        session_id = "audio_interrupt_test"
        user_id = "audio_interrupt_user"
        state_manager = await StateManager.create(workflow_name, session_id, user_id)

        print("[PIPELINE] Passing audio to STT...")
        stt_result = await state_manager.executePipelineState({"audio_path": audio_path})
        print(f"[STT RESULT] {stt_result}")
        transcript = stt_result.outputs.get("text")
        print(f"[TRANSCRIPT] {transcript}")

        await state_manager.transitionPipeline("preprocessing")
        pre_result = await state_manager.executePipelineState({"transcript": transcript})
        print(f"[PREPROCESSING RESULT] {pre_result}")
        pre_outputs = pre_result.outputs if hasattr(pre_result, 'outputs') else {}
        clean_text = pre_outputs.get("clean_text")
        intent = pre_outputs.get("intent")
        emotion = pre_outputs.get("emotion")

        await state_manager.transitionPipeline("processing")
        proc_result = await state_manager.executePipelineState({"clean_text": clean_text, "intent": intent, "emotion": emotion})
        print(f"[PROCESSING RESULT] {proc_result}")
        proc_outputs = proc_result.outputs if hasattr(proc_result, 'outputs') else {}
        llm_answer = proc_outputs.get("llm_answer")

        await state_manager.transitionPipeline("tts")
        tts_result = await state_manager.executePipelineState({"text": llm_answer})
        print(f"[TTS RESULT] {tts_result}")
        print("[INFO] TTS audio generated. StateManager will handle playback and interrupts.")
        print("[INFO] If you want to test interrupts, speak during TTS playback.")
        print("[PIPELINE COMPLETE]")
    finally:
        pass

if __name__ == "__main__":
    import sys
    import os
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))
    asyncio.run(run_audio_interrupt_pipeline()) 