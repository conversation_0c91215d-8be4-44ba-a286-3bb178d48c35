# Voice Agents Platform Architecture

This document describes the overall architecture of the Voice Agents Platform.

## Overview

The Voice Agents Platform is designed with a modular architecture that separates concerns into distinct layers:

- **Agents Layer**: Contains all AI agents grouped by functionality
- **Core Layer**: Runtime and platform logic
- **Configuration Layer**: Environment and configuration management
- **Data Layer**: Audio assets and training data
- **Frontend Layer**: User interface components

## Component Structure

### Agents
- `base/`: Abstract base classes and shared agent logic
- `stt/`: Speech-to-Text agents
- `tts/`: Text-to-Speech agents (OpenAI, ElevenLabs)
- `processing/`: Intent classification, disambiguation, emotion analysis
- `orchestration/`: Workflow orchestration agents
- `filler/`: Audio filler and transition agents

### Core
- `session/`: Session lifecycle management
- `state_manager/`: State control and transitions
- `orchestrator/`: Workflow execution engine
- `memory/`: Redis and MongoDB integration
- `logging/`: Centralized logging system

## Communication Patterns

The platform uses Redis pub/sub for inter-agent communication, with MongoDB for persistent storage.
