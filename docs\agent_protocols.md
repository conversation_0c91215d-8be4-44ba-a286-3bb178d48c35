# Agent Communication Protocols

This document describes the communication protocols used between agents in the Voice Agents Platform.

## Overview

Agents communicate using standardized message formats over Redis pub/sub channels.

## Message Format

All inter-agent messages follow a common schema defined in `schemas/a2a_message.py`.

## Communication Patterns

### Request-Response
For synchronous operations where an agent needs a response.

### Publish-Subscribe
For asynchronous notifications and state updates.

### Event Streaming
For real-time data flow between processing agents.

## Agent Types

### STT Agents
- Input: Audio streams
- Output: Transcribed text

### TTS Agents
- Input: Text messages
- Output: Audio streams

### Processing Agents
- Input: Text or structured data
- Output: Classified/processed results

### Orchestration Agents
- Input: State transitions
- Output: Workflow commands
