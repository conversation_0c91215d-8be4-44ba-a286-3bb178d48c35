"""
Test script for the new interrupt system with InterruptCoordinator.

This script demonstrates the complete interrupt handling flow:
1. StateManager executes TTS with interrupt support
2. Interrupt is detected and handled by InterruptCoordinator
3. System determines reversibility and handles accordingly
4. Workflow resumes or redirects based on action type
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.state_manager.state_manager import StateManager
from core.logging.logger_config import get_module_logger

logger = get_module_logger("interrupt_test")


async def test_interrupt_during_reversible_action():
    """Test interrupt handling during a reversible action (account balance)."""
    print("\n🎯 Testing Interrupt During Reversible Action (Account Balance)")
    print("=" * 60)
    
    try:
        # Initialize StateManager with banking workflow
        state_manager = await StateManager.create(
            workflow_name="banking_workflow.json",
            session_id="test_interrupt_reversible",
            user_id="test_user"
        )
        
        print(f"✅ StateManager initialized")
        print(f"   Current state: {state_manager.current_workflow_state_id}")
        print(f"   Interrupt handling: ✅ Available (via InterruptState)")
        
        # Simulate TTS execution for account balance (reversible action)
        print(f"\n📢 Starting TTS execution for account balance...")
        
        # Execute TTS step with interrupt support
        tts_input = {
            "text": "Your current account balance is $5,000. Is there anything else I can help you with?",
            "emotion": "neutral",
            "gender": "female"
        }
        
        # Start TTS execution (this would normally play audio)
        print(f"   TTS Input: {tts_input['text'][:50]}...")
        
        # Simulate interrupt after 2 seconds of TTS playback
        await asyncio.sleep(0.1)  # Simulate some TTS playback time
        
        print(f"\n🚨 INTERRUPT DETECTED!")
        print(f"   User interrupted during TTS playback")
        
        # Handle the interrupt
        interrupt_result = await state_manager.handle_interrupt(
            audio_data=b"simulated_audio_data",
            current_tts_audio_path="/tmp/balance_response.wav",
            playback_position=2.3,  # 2.3 seconds into playback
            user_input="Actually, I want to transfer money instead"
        )
        
        print(f"\n📋 Interrupt Handling Result:")
        print(f"   Status: {interrupt_result.status}")
        print(f"   Message: {interrupt_result.message}")
        print(f"   Outputs: {interrupt_result.outputs}")
        
        # Check if action was determined to be reversible
        action_reversible = interrupt_result.outputs.get("action_reversible", "unknown")
        should_queue = interrupt_result.outputs.get("should_queue_input", False)
        
        print(f"\n🔄 Action Analysis:")
        print(f"   Reversible: {action_reversible}")
        print(f"   Should queue input: {should_queue}")
        print(f"   Acknowledgment: {interrupt_result.outputs.get('acknowledgment_message', 'N/A')}")
        
        if action_reversible == "reversible":
            print(f"✅ SUCCESS: Reversible action correctly identified")
            print(f"   Expected behavior: Pause TTS, acknowledge, handle new request")
        else:
            print(f"❌ UNEXPECTED: Action should have been reversible")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        logger.error(f"Test failed: {str(e)}")
        return False


async def test_interrupt_during_irreversible_action():
    """Test interrupt handling during an irreversible action (money transfer)."""
    print("\n🎯 Testing Interrupt During Irreversible Action (Money Transfer)")
    print("=" * 60)
    
    try:
        # Initialize StateManager with banking workflow
        state_manager = await StateManager.create(
            workflow_name="banking_workflow.json",
            session_id="test_interrupt_irreversible",
            user_id="test_user"
        )
        
        # Transition to TransferFunds state
        await state_manager.transitionWorkflow("TransferFunds")
        print(f"✅ Transitioned to TransferFunds state")
        print(f"   Current state: {state_manager.current_workflow_state_id}")
        
        # Simulate TTS execution for money transfer confirmation (irreversible action)
        print(f"\n📢 Starting TTS execution for transfer confirmation...")
        
        # Simulate interrupt during transfer confirmation
        await asyncio.sleep(0.1)
        
        print(f"\n🚨 INTERRUPT DETECTED!")
        print(f"   User interrupted during transfer confirmation TTS")
        
        # Handle the interrupt
        interrupt_result = await state_manager.handle_interrupt(
            audio_data=b"simulated_audio_data",
            current_tts_audio_path="/tmp/transfer_confirmation.wav",
            playback_position=1.8,  # 1.8 seconds into playback
            user_input="Wait, cancel that transfer!"
        )
        
        print(f"\n📋 Interrupt Handling Result:")
        print(f"   Status: {interrupt_result.status}")
        print(f"   Message: {interrupt_result.message}")
        print(f"   Outputs: {interrupt_result.outputs}")
        
        # Check if action was determined to be irreversible
        action_reversible = interrupt_result.outputs.get("action_reversible", "unknown")
        
        print(f"\n🔄 Action Analysis:")
        print(f"   Reversible: {action_reversible}")
        print(f"   Acknowledgment: {interrupt_result.outputs.get('acknowledgment_message', 'N/A')}")
        
        if action_reversible == "irreversible":
            print(f"✅ SUCCESS: Irreversible action correctly identified")
            print(f"   Expected behavior: Complete action, acknowledge, explain completion")
        else:
            print(f"❌ UNEXPECTED: Action should have been irreversible")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        logger.error(f"Test failed: {str(e)}")
        return False


async def test_interrupt_system_integration():
    """Test interrupt system integration with StateManager."""
    print("\n🎯 Testing Interrupt System Integration")
    print("=" * 60)

    try:
        # Initialize StateManager
        state_manager = await StateManager.create(
            workflow_name="banking_workflow.json",
            session_id="test_interrupt_integration",
            user_id="test_user"
        )

        print(f"✅ StateManager initialized")

        # Check interrupt handling capability
        print(f"✅ Interrupt handling available via InterruptState")
        print(f"   Current TTS playback: {state_manager.current_tts_playback}")
        print(f"   Interrupt in progress: {state_manager.interrupt_in_progress}")
        
        # Test interrupt handling without active execution
        print(f"\n🧪 Testing interrupt handling without active execution...")
        
        interrupt_result = await state_manager.handle_interrupt(
            audio_data=b"test_audio",
            user_input="Hello, I need help"
        )
        
        print(f"   Result status: {interrupt_result.status}")
        print(f"   Result message: {interrupt_result.message}")
        
        if interrupt_result.status.value == "success":
            print(f"✅ SUCCESS: Interrupt handled successfully")
        else:
            print(f"⚠️  WARNING: Interrupt handling returned: {interrupt_result.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        logger.error(f"Integration test failed: {str(e)}")
        return False


async def main():
    """Run all interrupt system tests."""
    print("🚀 Starting Interrupt System Tests")
    print("=" * 60)
    
    tests = [
        ("Interrupt System Integration", test_interrupt_system_integration),
        ("Reversible Action Interrupt", test_interrupt_during_reversible_action),
        ("Irreversible Action Interrupt", test_interrupt_during_irreversible_action),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"🎉 ALL TESTS PASSED! Interrupt system is working correctly.")
    else:
        print(f"⚠️  Some tests failed. Check the logs for details.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
