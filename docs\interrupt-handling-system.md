# Voice Agent Interrupt Handling System Documentation

## **1. 📝 Feature Idea / Problem Statement**

**Description**: Voice AI systems require sophisticated interrupt handling to manage natural conversation flows where users may need to interrupt ongoing TTS playback. The interrupt handling system addresses the critical challenge of gracefully managing user interruptions during agent speech while maintaining conversation context and providing appropriate responses based on action reversibility.

**Context**: Real-world voice interactions face several interrupt-related challenges:
- **Natural Conversation Flow**: Users expect to interrupt AI agents just like human conversations
- **Action Timing Conflicts**: Users may interrupt after irreversible actions have been executed
- **Context Preservation**: Interrupted conversations must maintain state and resume appropriately
- **User Experience**: Harsh cutoffs or ignored interrupts create poor user experiences
- **System Reliability**: Interrupt handling must work consistently across different audio conditions

**Summary**: A comprehensive interrupt handling system that detects user voice input during TTS playback, implements graceful pause/resume functionality, provides context-aware acknowledgments based on action reversibility, and maintains conversation state throughout the interrupt process.

**Target Audience**: 
- **End Users**: Customers expecting natural, interruptible voice interactions
- **Voice UI Designers**: Teams building conversational interfaces requiring interrupt support
- **Customer Service Teams**: Agents using voice AI systems that need reliable interrupt handling
- **System Administrators**: Teams managing voice AI deployments requiring robust interrupt capabilities

## **2. 🥅 Key Goals & Benefits**

**Goals**:
- **Natural Conversation Flow**: Enable users to interrupt TTS playback naturally without harsh cutoffs
- **Context-Aware Responses**: Provide appropriate acknowledgments based on action reversibility
- **Graceful Recovery**: Resume interrupted TTS or process new user input based on context
- **Reliable Detection**: Accurately detect user voice input during TTS playback with minimal false positives
- **State Management**: Maintain conversation context and playback state throughout interrupt handling

**Business Benefits**:
- **Enhanced User Experience**: Natural interrupt handling improves conversation satisfaction
- **Reduced Frustration**: Users can correct mistakes or change direction mid-conversation
- **Improved Accessibility**: Better support for users with different communication patterns
- **Professional Interactions**: Polite acknowledgments maintain professional conversation tone
- **Error Recovery**: Users can interrupt to prevent or correct unwanted actions

**Technical Benefits**:
- **Modular Architecture**: Separate components for detection, handling, and recovery
- **Configurable Sensitivity**: Adjustable VAD thresholds and confirmation windows
- **Action Awareness**: Different handling strategies based on action reversibility
- **State Persistence**: Reliable playback position tracking and resume functionality
- **Comprehensive Logging**: Detailed interrupt event tracking for analysis and debugging

## **3. 📖 Research & Business Value**

**Market Research**: Voice AI systems with natural interrupt handling show significantly higher user satisfaction and adoption rates, particularly in customer service and accessibility applications.

**User Feedback**: Users expect voice AI to behave like human conversations, including the ability to interrupt and be acknowledged appropriately.

**Accessibility Requirements**: Interrupt handling is crucial for users with speech patterns that may require multiple attempts or corrections during voice interactions.

## **4. 🏗️ Solution (How it should work)**

**Proposed Solution**: 
A multi-layered interrupt handling system with the following components:

- **Voice Activity Detection (VAD)**: Real-time detection of user speech during TTS playback
- **Confirmation Window**: 0.5-second grace period to confirm genuine interrupts vs. background noise
- **Action Reversibility Analysis**: Context-aware determination of appropriate response strategies
- **Graceful Acknowledgment**: Polite responses based on whether actions are reversible or irreversible
- **State Management**: Playback position tracking and resume functionality
- **Memory Integration**: Interrupt context storage for StateManager orchestration

**User Journey**:
1. **Agent Speaks** → TTS playback begins with concurrent interrupt detection
2. **User Interrupts** → VAD detects voice activity during playback
3. **Confirmation Period** → System waits 0.5s to confirm genuine interrupt
4. **Graceful Pause** → TTS playback pauses, position saved
5. **Context Analysis** → System determines action reversibility
6. **Appropriate Response** → Acknowledgment based on reversibility status
7. **Flow Decision** → Resume TTS or process new user input based on context

**Business Value Flow**:
- **Natural Interactions**: Users can interrupt naturally without system confusion
- **Error Prevention**: Users can stop unwanted actions before completion
- **Professional Experience**: Polite acknowledgments maintain conversation quality
- **Flexible Recovery**: System adapts response based on situation context

## **5. 🧑🏻‍💻 Technical Implementation**

### 📋 Core Components

**TTSState (Enhanced with Interrupt Handling)**
- Orchestrates TTS playback with concurrent interrupt detection
- Manages TTSPlaybackController for audio playback and monitoring
- Handles interrupt detection callbacks and context storage
- Integrates with action reversibility analysis

**TTSPlaybackController (utils/audio_utils.py)**
- Controls audio playback with pause/resume functionality
- Implements concurrent interrupt detection during playback
- Manages playback state and position tracking
- Provides interrupt callbacks for state coordination

**InterruptHandlerState (core/interruption/interrupt_handler_state.py)**
- Processes interrupt events with grace period confirmation
- Determines appropriate acknowledgment messages
- Coordinates with ActionReversibilityDetector for context analysis
- Manages interrupt flow decisions (resume vs. new processing)

**ActionReversibilityDetector (core/interruption/action_reversibility.py)**
- Analyzes action reversibility from explicit context configuration
- Provides context-appropriate interrupt acknowledgment messages
- Determines handling strategy based on action characteristics
- Supports configurable reversibility policies

**AudioProcessor with VAD (utils/audio_utils.py)**
- Implements Voice Activity Detection using webrtcvad and energy-based fallbacks
- Applies bandpass filtering for human speech frequency range (300-3400 Hz)
- Provides configurable VAD thresholds and sensitivity settings
- Handles multiple audio formats and sample rates

**InterruptConfig System (core/config/interrupt_config.py)**
- Manages configurable interrupt detection parameters
- Provides VAD threshold, confirmation window, and sensitivity settings
- Supports per-deployment configuration customization
- Includes default configuration with sensible fallbacks

### 🔄 Interrupt Detection Flow

**1. TTS Playback Initialization**
```python
# TTSState starts playback with interrupt detection
playback_result = await self.playback_controller.start_playback_with_interrupt_detection(
    audio_path, interrupt_callback, resume_from_position=resume_position
)
```

**2. Concurrent VAD Monitoring**
```python
# TTSPlaybackController runs interrupt detection loop
async def _interrupt_detection_loop(self):
    while self.is_playing:
        # Monitor for voice activity during playback
        if await self._detect_user_voice():
            await self._handle_interrupt()
```

**3. Grace Period Confirmation**
```python
# InterruptHandlerState confirms interrupt
interrupt_confirmed = await self._confirm_interrupt(validated_input)
if not interrupt_confirmed:
    return "False alarm - resuming playback"
```

**4. Action Reversibility Analysis**
```python
# ActionReversibilityDetector analyzes context
action_metadata = self.reversibility_detector.get_action_metadata(context)
acknowledgment = action_metadata.interrupt_message
```

**5. Context Storage and Orchestration**
```python
# TTSState stores interrupt context for StateManager
await memory_manager.set_interrupt_context(
    detected=True, confirmed=True, user_input_queued=user_input,
    resume_after_acknowledgment=action_reversible
)
```

### 🎯 Action Reversibility Strategies

The system implements different interrupt handling strategies based on action reversibility:

**Reversible Actions (reversible: true)**
- **Examples**: Information queries, menu navigation, preference changes
- **Interrupt Response**: "Allow me to finish this first, then I'll respond to what you said."
- **Flow**: Resume TTS completion → Process new user input
- **User Input**: Queued for processing after TTS completion

**Irreversible Actions (reversible: false)**
- **Examples**: Financial transfers, data deletions, external API calls
- **Interrupt Response**: "The action has already been completed. If something went wrong, let me know and I'll help fix it."
- **Flow**: Complete TTS → No input queuing (action already done)
- **User Input**: Not queued (action cannot be undone)

**Unknown Reversibility (reversible: null/undefined)**
- **Examples**: Actions without explicit reversibility configuration
- **Interrupt Response**: "I understand you want to say something. Allow me to finish this first, then I'll respond to what you said."
- **Flow**: Default to reversible behavior for safety
- **User Input**: Queued for processing after TTS completion

### 📊 Configuration System

**Default Configuration (configs/interrupt_config_default.json)**
```json
{
  "detection": {
    "enabled": true,
    "vad_threshold": 0.01,
    "confirmation_window_seconds": 0.5,
    "min_interrupt_duration_seconds": 0.3
  }
}
```

**Configuration Parameters**:
- **enabled**: Master switch for interrupt detection
- **vad_threshold**: Voice activity detection sensitivity (0.01 = sensitive, 0.1 = less sensitive)
- **confirmation_window_seconds**: Grace period to confirm genuine interrupts (0.5s recommended)
- **min_interrupt_duration_seconds**: Minimum duration to consider as valid interrupt

### 🔍 Voice Activity Detection (VAD)

**Primary Method: webrtcvad**
- Robust voice activity detection using Google's WebRTC VAD
- Supports multiple sample rates (8kHz, 16kHz, 32kHz, 48kHz)
- Configurable aggressiveness levels (0-3, where 3 is most aggressive)
- Bandpass filtering for human speech frequency range (300-3400 Hz)

**Fallback Method: Energy-based Detection**
- Simple amplitude/energy analysis when webrtcvad is unavailable
- Configurable energy threshold for voice detection
- Less accurate but provides reliable fallback functionality

**Audio Processing Pipeline**:
1. **Format Conversion**: Convert audio to 16-bit mono PCM
2. **Bandpass Filtering**: Filter to human speech frequencies
3. **Window Analysis**: Process audio in 30ms windows
4. **VAD Application**: Apply webrtcvad or energy-based detection
5. **Threshold Evaluation**: Determine voice activity based on configured thresholds

### 💾 State Management and Memory Integration

**Interrupt Context Storage**
```python
# Stored in contextual memory for StateManager coordination
interrupt_context = {
    "detected": True,
    "confirmed": True,
    "user_input_queued": "user's interrupt text",
    "resume_after_acknowledgment": True/False,
    "action_reversible": True/False,
    "interrupt_timestamp": "2024-01-01T12:00:00Z"
}
```

**TTS Playback State**
```python
# Stored for resume functionality
tts_playback_state = {
    "audio_path": "/path/to/audio.mp3",
    "status": "interrupted",
    "playback_position": 2.5,  # seconds
    "message_hash": "sha256_hash_of_audio_path"
}
```

**Interrupt Event Logging**
```python
# Detailed event tracking for analysis
interrupt_event = {
    "event_type": "interruption_detected",
    "timestamp": "2024-01-01T12:00:00Z",
    "session_id": "sess_123",
    "playback_position": 2.5,
    "user_input": "wait, I meant 500 not 5000",
    "action_reversible": False,
    "acknowledgment_sent": "The action has already been completed..."
}
```

## **6. 🤝🏻 Integration with Other Parts of the System**

**StateManager Integration**:
- Monitors interrupt context in memory for orchestration decisions
- Handles interrupt flow transitions between states
- Coordinates resume vs. new input processing based on reversibility
- Manages workflow state transitions during interrupt handling

**Memory Manager Integration**:
- Stores interrupt context for cross-component coordination
- Maintains TTS playback state for resume functionality
- Logs interrupt events for analytics and debugging
- Provides interrupt status queries for StateManager

**Agent Integration**:
- TTSAgent works with TTSState for interrupt-aware playback
- All agents respect interrupt flags during processing
- Interrupt detection doesn't interfere with other agent operations
- Graceful handling when interrupts occur during agent processing

**Configuration Integration**:
- Centralized interrupt configuration management
- Per-deployment customization of VAD sensitivity
- Runtime configuration updates without system restart
- Default fallback configurations for reliability

### 🛠️ How to Use & Extend

**Basic Usage in TTSState**:
```python
# Enable interrupt handling in TTSState
tts_state = TTSState(
    state_id="tts_process",
    agent_registry=agent_registry,
    session_id=session_id,
    interrupt_config=interrupt_config
)

# Process with automatic interrupt detection
result = await tts_state.process(input_data, context)
```

**Configuring Action Reversibility**:
```python
# In workflow state configuration
state_config = {
    "reversible": False,  # Irreversible action
    "has_side_effect": True,
    "post_tts_policy": "continue_and_explain"
}
```

**Custom VAD Thresholds**:
```json
{
  "detection": {
    "enabled": true,
    "vad_threshold": 0.005,  # More sensitive
    "confirmation_window_seconds": 0.3,  # Faster confirmation
    "min_interrupt_duration_seconds": 0.2
  }
}
```

**Extending Reversibility Detection**:
```python
# Add custom reversibility logic
class CustomReversibilityDetector(ActionReversibilityDetector):
    def get_action_metadata(self, context):
        # Custom logic for determining reversibility
        if context.get("action_type") == "custom_action":
            return ActionMetadata(
                reversibility=ReversibilityLevel.IRREVERSIBLE,
                interrupt_message="Custom interrupt message"
            )
        return super().get_action_metadata(context)
```

## **7. 🧪 Test Cases**

### Unit Tests

**VAD Detection Tests**:
- Test voice activity detection with various audio samples
- Verify threshold sensitivity and false positive rates
- Test bandpass filtering effectiveness
- Validate fallback detection methods

**Interrupt Confirmation Tests**:
- Test grace period confirmation with different durations
- Verify false alarm detection and filtering
- Test confirmation with background noise
- Validate minimum duration requirements

**Action Reversibility Tests**:
- Test reversibility detection with explicit configuration
- Verify appropriate acknowledgment message selection
- Test unknown reversibility handling
- Validate context-based decision making

### Integration Tests

**End-to-End Interrupt Flow**:
- Complete interrupt detection → confirmation → acknowledgment → resume
- Test with both reversible and irreversible actions
- Verify state management throughout interrupt process
- Test memory context storage and retrieval

**TTS Playback Integration**:
- Test interrupt detection during actual TTS playback
- Verify playback position tracking and resume functionality
- Test multiple interrupts during single TTS session
- Validate audio file handling and cleanup

**StateManager Coordination**:
- Test StateManager monitoring of interrupt context
- Verify proper workflow transitions during interrupts
- Test queued input processing after TTS completion
- Validate error handling when StateManager is unavailable

### Performance Tests

**Real-Time VAD Performance**:
- Test VAD processing latency during TTS playback
- Verify system performance with concurrent audio processing
- Test memory usage during extended interrupt detection
- Validate CPU usage with multiple concurrent sessions

**Interrupt Response Time**:
- Measure time from voice detection to TTS pause
- Test acknowledgment generation and delivery speed
- Verify resume functionality performance
- Validate overall interrupt handling latency
