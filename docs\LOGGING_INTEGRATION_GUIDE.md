# Voice Agents Platform - Logging Integration Guide

This guide explains how to use the integrated custom logging system throughout the Voice Agents Platform.

## Overview

The Voice Agents Platform uses a custom structured logging system built on top of the core logger (`core/logger.py`) that provides:

- **Structured JSONL logging** with thread-safe concurrent writes
- **File rotation** for managing log sizes
- **Three separate log files**: conversations, metrics, and errors
- **Rich metadata** including session_id, state_id, trace_id, etc.
- **Decorator support** for automatic function logging
- **Module-specific loggers** with automatic context management

## Quick Start

### 1. Basic Setup

```python
import sys
from pathlib import Path

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logger_config import setup_development_logging, get_module_logger

# Set up logging (do this once at application startup)
setup_development_logging()

# Get a module-specific logger
logger = get_module_logger("my_module", session_id="session_123")
```

### 2. Basic Logging

```python
# Simple logging
logger.info("Processing user request")
logger.error("Something went wrong")

# Structured logging with context
logger.info(
    "Processing user request",
    action="process_request",
    input_data={"user_id": "123", "request_type": "balance_check"},
    layer="request_processing"
)
```

### 3. Using with StateOutput

```python
from core.logger import StateOutput

state_output = StateOutput(
    status="success",
    data={"balance": 1000},
    metadata={"currency": "USD"}
)

logger.log_state_output(
    level="info",
    message="Balance retrieved successfully",
    state_output=state_output,
    action="get_balance",
    layer="banking"
)
```

## Environment Configuration

### Environment Variables

Set these environment variables to configure logging:

```bash
# Environment (development, testing, production)
export VOICE_AGENT_ENV=development

# Optional overrides
export VOICE_AGENT_LOG_DIR=logs
export VOICE_AGENT_LOG_LEVEL=debug
export VOICE_AGENT_LOG_MAX_BYTES=1048576
export VOICE_AGENT_LOG_BACKUP_COUNT=5
```

### Programmatic Configuration

```python
from core.logger_config import LoggerConfig

# Set up for different environments
logger = LoggerConfig.setup_for_development()
logger = LoggerConfig.setup_for_testing()
logger = LoggerConfig.setup_for_production()

# Custom configuration
custom_config = {
    "log_dir": "custom_logs",
    "verbosity": "info",
    "max_bytes": 5 * 10**6,  # 5MB
    "backup_count": 10
}
logger = LoggerConfig.setup_logger("production", custom_config)
```

## Module Integration Patterns

### 1. State Management

```python
from core.logger_config import get_module_logger

class StateManager:
    def __init__(self, session_id: str):
        self.logger = get_module_logger("state_manager", session_id=session_id)
    
    async def execute_step(self, input_data):
        self.logger.info(
            "Starting state execution",
            action="execute_step",
            input_data={"current_state": self.current_state_id},
            layer="state_management"
        )
        # ... execution logic ...
```

### 2. Agent Classes

```python
from agents.base_agent import BaseAgent

class MyAgent(BaseAgent):
    def __init__(self, session_id: str = None):
        super().__init__("my_agent", session_id)
    
    async def process(self, input_data, context=None):
        # Logging is automatically handled by BaseAgent.safe_process()
        return StateOutput(...)
```

### 3. Utility Functions

```python
from core.logger_config import get_module_logger

logger = get_module_logger("audio_utils")

def process_audio(audio_data: bytes, session_id: str = None):
    logger.info(
        "Processing audio data",
        action="process_audio",
        input_data={"audio_size": len(audio_data)},
        layer="audio_processing",
        session_id=session_id
    )
    # ... processing logic ...
```

## Logging Best Practices

### 1. Use Structured Fields

Always include these key fields for better traceability:

```python
logger.info(
    "Clear descriptive message",
    action="specific_action_name",           # What action is being performed
    input_data={"key": "value"},            # Input parameters
    output_data={"result": "value"},        # Results/outputs
    layer="component_layer",                # Which layer/component
    session_id=session_id,                  # Session context
    state_id=state_id,                      # State context
    metrics={"duration_ms": 150},           # Performance metrics
    status="success",                       # Operation status
    reason="error_description"              # Error reason (for errors)
)
```

### 2. Layer Naming Convention

Use consistent layer names:

- `"state_management"` - State execution and transitions
- `"memory_ephemeral"`, `"memory_contextual"`, `"memory_persistent"` - Memory operations
- `"agent"` - Agent processing
- `"audio_processing"` - Audio operations
- `"language_utils"` - Language detection/processing
- `"validation_utils"` - Input validation
- `"schema_loading"` - Schema loading operations
- `"transition"` - State transition evaluation

### 3. Error Handling

```python
try:
    result = risky_operation()
    logger.info(
        "Operation completed successfully",
        action="risky_operation",
        output_data=result,
        layer="processing"
    )
except Exception as e:
    logger.error(
        "Operation failed",
        action="risky_operation",
        reason=str(e),
        layer="processing"
    )
    raise
```

### 4. Performance Monitoring

```python
import time

start_time = time.time()
try:
    result = expensive_operation()
    duration_ms = (time.time() - start_time) * 1000
    
    logger.info(
        "Expensive operation completed",
        action="expensive_operation",
        output_data=result,
        metrics={"duration_ms": duration_ms},
        layer="processing"
    )
except Exception as e:
    duration_ms = (time.time() - start_time) * 1000
    logger.error(
        "Expensive operation failed",
        action="expensive_operation",
        reason=str(e),
        metrics={"duration_ms": duration_ms},
        layer="processing"
    )
```

## Log File Structure

The logging system creates three separate log files:

### 1. Conversations Log (`conversations.jsonl`)
- User interactions and conversation flow
- State transitions and workflow execution
- Agent responses and processing

### 2. Metrics Log (`metrics.jsonl`)
- Performance metrics and timing data
- System resource usage
- Operation statistics

### 3. Errors Log (`errors.jsonl`)
- Error messages and stack traces
- Failed operations and exceptions
- System warnings and alerts

## Context Management

### Session and State Context

```python
# Initialize with context
logger = get_module_logger("my_module", session_id="session_123", state_id="state_001")

# Update context as needed
logger.update_context(session_id="new_session", state_id="new_state")

# Context is automatically included in all log entries
logger.info("This will include session and state context")
```

### Trace ID for Request Tracking

```python
import uuid

trace_id = str(uuid.uuid4())

logger.info(
    "Starting request processing",
    action="process_request",
    trace_id=trace_id,
    layer="request_handler"
)

# Pass trace_id through the call chain for end-to-end tracing
```

## Cleanup and Resource Management

Always clean up logger resources:

```python
from core.logger_config import cleanup_logger

try:
    # Your application logic
    main_application()
finally:
    # Clean up logger resources
    cleanup_logger()
```

## Integration Examples

See the following files for complete integration examples:

- `core/state_mgmt/StateManager.py` - State management integration
- `core/state_mgmt/memory_manager.py` - Memory operations integration
- `agents/base_agent.py` - Agent base classes with logging
- `utils/language_utils.py` - Utility module integration
- `core/__main__.py` - Application startup and cleanup

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure the project root is added to sys.path before importing logger modules
2. **Permission Errors**: Check that the log directory is writable
3. **File Rotation Issues**: Ensure sufficient disk space for log files
4. **Performance Issues**: Reduce log verbosity in production environments

### Debug Mode

Enable debug logging for troubleshooting:

```python
from core.logger_config import LoggerConfig

# Enable debug logging
logger = LoggerConfig.setup_logger("development", {"verbosity": "debug"})
```

This will provide detailed logging information for debugging issues.
