# Application Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DEFAULT_TTL=3600
REDIS_PASSWORD= # left empty for no password
REDIS_DB=0

# Qdrant Configuration
QDRANT_HOST=qdrant
QDRANT_PORT=6333
QDRANT_API_KEY= # optional

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini

# Application Specific
SESSION_TIMEOUT=3600
MAX_MEMORY_SIZE=1000

# Database Configuration (if needed)
DATABASE_URL=sqlite:///./voice_agents.db

# Logging Configuration
LOG_FILE_PATH=./logs/voice_agents.log
LOG_FORMAT=json

ELEVENLABS_API_KEY=
