from pathlib import Path
import sys
import os

project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

import unittest
from unittest import mock
from unittest.mock import MagicMock, patch, AsyncMock
import asyncio
from agents.base.base_agent import BaseAgent
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType



class ConcreteAgent(BaseAgent):
    async def process(self, input_data, context=None):
        return StateOutput(
            status=StatusType.SUCCESS,
            message="Processed successfully",
            code=StatusCode.OK,
            outputs={"echo": input_data},
            meta={"context": context}
        )
    
    def validate_input(self, input_data):
        if not isinstance(input_data, dict) or input_data is None:
            return False
        return True

class TestBaseAgent(unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        # Patch RedisClient and logger
        patcher_redis = patch('agents.base_agent.RedisClient', autospec=True)
        patcher_logger = patch('agents.base_agent.get_module_logger', autospec=True)
        self.mock_redis = patcher_redis.start()
        self.mock_logger = patcher_logger.start()
        self.addCleanup(patcher_redis.stop)
        self.addCleanup(patcher_logger.stop)
        self.agent = ConcreteAgent(agent_name="test_agent", session_id="sess1", state_id="state1")
        self.agent.logger = MagicMock()
        self.agent.redis_client = AsyncMock()

    async def test_init_and_update_context(self):
        self.assertEqual(self.agent.agent_name, "test_agent")
        self.assertEqual(self.agent.session_id, "sess1")
        self.assertEqual(self.agent.state_id, "state1")
        self.agent.update_context(session_id="sess2", state_id="state2")
        self.assertEqual(self.agent.session_id, "sess2")
        self.assertEqual(self.agent.state_id, "state2")
        self.agent.logger.update_context.assert_called_with("sess2", "state2")

    async def test_log_methods(self):
        # _log_process_start
        self.agent._log_process_start({"foo": 1}, {"bar": 2})
        self.agent.logger.info.assert_called()
        # _log_process_end
        result = StateOutput(status=StatusType.SUCCESS, message="done", code=StatusCode.OK, outputs={}, meta={})
        self.agent._log_process_end(result, 123.4)
        self.agent.logger.info.assert_called()
        # _log_error
        self.agent._log_error(Exception("fail"), {"x": 1})
        self.agent.logger.error.assert_called()

    async def test_safe_process_success(self):
        result = await self.agent.safe_process({"foo": "bar"}, {"baz": 1})
        self.assertIsInstance(result, StateOutput)
        self.assertEqual(result.status, StatusType.SUCCESS)
        self.agent.logger.info.assert_any_call(
            f"Starting {self.agent.agent_name} processing",
            action="process_start",
            input_data={"input": {"foo": "bar"}, "context": {"baz": 1}},
            layer="agent",
            agent_name=self.agent.agent_name
        )
        self.agent.logger.info.assert_any_call(
            f"Completed {self.agent.agent_name} processing",
            action="process_end",
            output_data={"status": StatusType.SUCCESS.value, "message": "Processed successfully"},
            metrics={"duration_ms": mock.ANY},
            layer="agent",
            agent_name=self.agent.agent_name
        )

    async def test_safe_process_error(self):
        class ErrorAgent(ConcreteAgent):
            async def process(self, input_data, context=None):
                raise ValueError("fail")
        agent = ErrorAgent(agent_name="err_agent")
        agent.logger = MagicMock()
        result = await agent.safe_process({"foo": "bar"})
        self.assertEqual(result.status, StatusType.ERROR)
        self.assertIn("fail", result.message)
        agent.logger.error.assert_called()

    def test_input_output_schema(self):
        self.assertEqual(self.agent.input_schema(), {})
        self.assertEqual(self.agent.output_schema(), {})

    async def test_setup_and_teardown(self):
        self.assertIsNone(await self.agent.setup())
        self.assertIsNone(await self.agent.teardown())

    def test_agent_type_property(self):
        self.assertEqual(self.agent.agent_type, "ConcreteAgent")

    def test_a2a_channel_property(self):
        self.assertEqual(self.agent.a2a_channel, "agent_channel::test_agent")

    def test_validate_input_and_is_ready(self):
        self.assertFalse(self.agent.validate_input("not a dict"))
        self.assertTrue(self.agent.is_ready())

    async def test_handle_message(self):
        # Prepare a fake A2AMessage
        msg = A2AMessage(
            session_id="sessX",
            message_type=MessageType.INSTRUCTION,
            source_agent="src",
            target_agent="test_agent",
            payload={"foo": "bar"},
            context_keys_updated=None
        )
        self.agent.safe_process = AsyncMock(return_value=StateOutput(
            status=StatusType.SUCCESS,
            message="ok",
            code=StatusCode.OK,
            outputs={},
            meta={}
        ))
        await self.agent.handle_message(msg.to_json())
        self.agent.redis_client.publish.assert_awaited()

    async def test_start_a2a_loop(self):
        self.agent.redis_client.subscribe = AsyncMock()
        await self.agent.start_a2a_loop()
        self.agent.redis_client.subscribe.assert_awaited_with(self.agent.a2a_channel, self.agent.handle_message)

    async def test_safe_process_with_invalid_input(self):
        result = await self.agent.safe_process("not_a_dict")
        self.assertEqual(result.status, StatusType.ERROR)
        result = await self.agent.safe_process(None)
        self.assertEqual(result.status, StatusType.ERROR)

    async def test_handle_message_with_malformed_json(self):
        # Not a JSON string
        await self.agent.handle_message("not a json string")
        self.agent.logger.error.assert_called()
        # JSON but missing required fields
        bad_json = '{"foo": "bar"}'
        await self.agent.handle_message(bad_json)
        self.agent.logger.error.assert_called()

    async def test_handle_message_with_process_error(self):
        # safe_process raises error
        self.agent.safe_process = AsyncMock(side_effect=Exception("process failed"))
        msg = A2AMessage(
            session_id="sessX",
            message_type=MessageType.INSTRUCTION,
            source_agent="src",
            target_agent="test_agent",
            payload={"foo": "bar"},
            context_keys_updated=None
        )
        await self.agent.handle_message(msg.to_json())
        self.agent.logger.error.assert_called()

    def test_update_context_with_none(self):
        # Should not raise, should not update context
        old_session = self.agent.session_id
        old_state = self.agent.state_id
        self.agent.update_context(session_id=None, state_id=None)
        self.assertEqual(self.agent.session_id, old_session)
        self.assertEqual(self.agent.state_id, old_state)

if __name__ == "__main__":
    unittest.main() 