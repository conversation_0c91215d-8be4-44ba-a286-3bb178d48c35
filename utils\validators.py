"""
Validation utilities for the Voice Agents Platform.

This module provides input validation, data sanitization, schema validation,
and security checks with integrated structured logging.
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
import re
import json
from datetime import datetime
import uuid

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logging.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode

# Module logger
logger = get_module_logger("validation_utils")


class InputValidator:
    """Input validation utilities with logging."""
    
    def __init__(self):
        # Common validation patterns
        self.patterns = {
            'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            'phone': r'^\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$',
            'account_id': r'^[A-Za-z0-9]{6,20}$',
            'session_id': r'^[a-f0-9-]{36}$',  # UUID format
            'amount': r'^\d+(\.\d{1,2})?$',
            'date': r'^\d{4}-\d{2}-\d{2}$',
            'time': r'^\d{2}:\d{2}(:\d{2})?$'
        }
        
        logger.info(
            "Initialized InputValidator",
            action="initialize",
            output_data={"validation_patterns": len(self.patterns)},
            layer="validation_utils"
        )
    
    def validate_input(self, input_data: Dict[str, Any], schema: Dict[str, Any], session_id: str = None) -> StateOutput:
        """
        Validate input data against a schema.
        
        Args:
            input_data: Data to validate
            schema: Validation schema
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with validation results
        """
        try:
            logger.info(
                "Starting input validation",
                action="validate_input",
                input_data={
                    "input_keys": list(input_data.keys()) if input_data else [],
                    "schema_keys": list(schema.keys()) if schema else []
                },
                layer="validation_utils",
                session_id=session_id
            )
            
            if not input_data:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No input data provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_input"}
                )
            
            if not schema:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No validation schema provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_schema"}
                )
            
            validation_errors = []
            validated_data = {}
            
            # Check required fields
            required_fields = schema.get('required', [])
            for field in required_fields:
                if field not in input_data:
                    validation_errors.append(f"Required field '{field}' is missing")
                elif input_data[field] is None or input_data[field] == '':
                    validation_errors.append(f"Required field '{field}' is empty")
            
            # Validate each field
            field_schemas = schema.get('fields', {})
            for field_name, field_schema in field_schemas.items():
                if field_name in input_data:
                    value = input_data[field_name]
                    field_errors = self._validate_field(field_name, value, field_schema)
                    validation_errors.extend(field_errors)
                    
                    if not field_errors:  # Only add if valid
                        validated_data[field_name] = value
            
            # Check for unexpected fields if strict mode
            if schema.get('strict', False):
                allowed_fields = set(field_schemas.keys())
                provided_fields = set(input_data.keys())
                unexpected_fields = provided_fields - allowed_fields
                for field in unexpected_fields:
                    validation_errors.append(f"Unexpected field '{field}' provided")
            
            is_valid = len(validation_errors) == 0
            
            result = {
                "is_valid": is_valid,
                "errors": validation_errors,
                "validated_data": validated_data,
                "error_count": len(validation_errors)
            }
            
            logger.info(
                "Input validation completed",
                action="validate_input",
                output_data={
                    "is_valid": is_valid,
                    "error_count": len(validation_errors),
                    "validated_fields": len(validated_data)
                },
                layer="validation_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS if is_valid else StatusType.ERROR,
                message="Validation passed" if is_valid else f"Validation failed with {len(validation_errors)} errors",
                code=StatusCode.OK if is_valid else StatusCode.BAD_REQUEST,
                outputs=result,
                meta={"is_valid": is_valid}
            )
            
        except Exception as e:
            logger.error(
                "Error in input validation",
                action="validate_input",
                reason=str(e),
                layer="validation_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Validation failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def _validate_field(self, field_name: str, value: Any, field_schema: Dict[str, Any]) -> List[str]:
        """Validate a single field against its schema."""
        errors = []
        
        # Type validation
        expected_type = field_schema.get('type')
        if expected_type:
            if expected_type == 'string' and not isinstance(value, str):
                errors.append(f"Field '{field_name}' must be a string")
            elif expected_type == 'number' and not isinstance(value, (int, float)):
                errors.append(f"Field '{field_name}' must be a number")
            elif expected_type == 'integer' and not isinstance(value, int):
                errors.append(f"Field '{field_name}' must be an integer")
            elif expected_type == 'boolean' and not isinstance(value, bool):
                errors.append(f"Field '{field_name}' must be a boolean")
            elif expected_type == 'array' and not isinstance(value, list):
                errors.append(f"Field '{field_name}' must be an array")
            elif expected_type == 'object' and not isinstance(value, dict):
                errors.append(f"Field '{field_name}' must be an object")
        
        # Pattern validation
        pattern = field_schema.get('pattern')
        if pattern and isinstance(value, str):
            if pattern in self.patterns:
                pattern = self.patterns[pattern]
            if not re.match(pattern, value):
                errors.append(f"Field '{field_name}' does not match required pattern")
        
        # Length validation
        min_length = field_schema.get('min_length')
        max_length = field_schema.get('max_length')
        if isinstance(value, str):
            if min_length and len(value) < min_length:
                errors.append(f"Field '{field_name}' must be at least {min_length} characters")
            if max_length and len(value) > max_length:
                errors.append(f"Field '{field_name}' must be at most {max_length} characters")
        
        # Range validation
        min_value = field_schema.get('min_value')
        max_value = field_schema.get('max_value')
        if isinstance(value, (int, float)):
            if min_value is not None and value < min_value:
                errors.append(f"Field '{field_name}' must be at least {min_value}")
            if max_value is not None and value > max_value:
                errors.append(f"Field '{field_name}' must be at most {max_value}")
        
        # Enum validation
        allowed_values = field_schema.get('enum')
        if allowed_values and value not in allowed_values:
            errors.append(f"Field '{field_name}' must be one of: {', '.join(map(str, allowed_values))}")
        
        return errors
    
    def sanitize_input(self, input_data: Dict[str, Any], session_id: str = None) -> StateOutput:
        """
        Sanitize input data to prevent security issues.
        
        Args:
            input_data: Data to sanitize
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with sanitized data
        """
        try:
            logger.info(
                "Starting input sanitization",
                action="sanitize_input",
                input_data={"input_keys": list(input_data.keys()) if input_data else []},
                layer="validation_utils",
                session_id=session_id
            )
            
            if not input_data:
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="No input data to sanitize",
                    code=StatusCode.OK,
                    outputs={},
                    meta={}
                )
            
            sanitized_data = {}
            sanitization_log = []
            
            for key, value in input_data.items():
                original_value = value
                
                if isinstance(value, str):
                    # Remove potentially dangerous characters
                    value = re.sub(r'[<>"\']', '', value)
                    
                    # Remove excessive whitespace
                    value = re.sub(r'\s+', ' ', value).strip()
                    
                    # Limit length to prevent DoS
                    if len(value) > 10000:
                        value = value[:10000]
                        sanitization_log.append(f"Truncated field '{key}' to 10000 characters")
                    
                    if value != original_value:
                        sanitization_log.append(f"Sanitized field '{key}'")
                
                elif isinstance(value, dict):
                    # Recursively sanitize nested objects
                    nested_result = self.sanitize_input(value, session_id)
                    if nested_result.status == StatusType.SUCCESS:
                        value = nested_result.outputs
                        if nested_result.meta.get('sanitization_log'):
                            sanitization_log.extend(nested_result.meta['sanitization_log'])
                
                elif isinstance(value, list):
                    # Sanitize list items
                    sanitized_list = []
                    for item in value:
                        if isinstance(item, str):
                            item = re.sub(r'[<>"\']', '', item)
                            item = re.sub(r'\s+', ' ', item).strip()
                        sanitized_list.append(item)
                    value = sanitized_list
                
                sanitized_data[key] = value
            
            result = sanitized_data
            
            logger.info(
                "Input sanitization completed",
                action="sanitize_input",
                output_data={
                    "sanitized_fields": len(sanitized_data),
                    "changes_made": len(sanitization_log)
                },
                layer="validation_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Input sanitized successfully ({len(sanitization_log)} changes made)",
                code=StatusCode.OK,
                outputs=result,
                meta={"sanitization_log": sanitization_log}
            )
            
        except Exception as e:
            logger.error(
                "Error in input sanitization",
                action="sanitize_input",
                reason=str(e),
                layer="validation_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Input sanitization failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )


class SecurityValidator:
    """Security validation utilities."""
    
    def __init__(self):
        # Common security patterns to detect
        self.security_patterns = {
            'sql_injection': [
                r'(\bUNION\b|\bSELECT\b|\bINSERT\b|\bDELETE\b|\bUPDATE\b|\bDROP\b)',
                r'(--|#|/\*|\*/)',
                r'(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+'
            ],
            'xss': [
                r'<script[^>]*>.*?</script>',
                r'javascript:',
                r'on\w+\s*=',
                r'<iframe[^>]*>.*?</iframe>'
            ],
            'path_traversal': [
                r'\.\./|\.\.\\'
            ]
        }
        
        logger.info(
            "Initialized SecurityValidator",
            action="initialize",
            layer="validation_utils"
        )
    
    def check_security_threats(self, input_data: Dict[str, Any], session_id: str = None) -> StateOutput:
        """
        Check input data for potential security threats.
        
        Args:
            input_data: Data to check
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with security assessment
        """
        try:
            logger.info(
                "Starting security threat check",
                action="check_security",
                input_data={"input_keys": list(input_data.keys()) if input_data else []},
                layer="validation_utils",
                session_id=session_id
            )
            
            threats_found = []
            threat_details = {}
            
            def check_value(key: str, value: Any, path: str = ""):
                current_path = f"{path}.{key}" if path else key
                
                if isinstance(value, str):
                    for threat_type, patterns in self.security_patterns.items():
                        for pattern in patterns:
                            if re.search(pattern, value, re.IGNORECASE):
                                threat_info = {
                                    "type": threat_type,
                                    "field": current_path,
                                    "pattern": pattern,
                                    "value_preview": value[:100] + "..." if len(value) > 100 else value
                                }
                                threats_found.append(threat_info)
                                
                                if threat_type not in threat_details:
                                    threat_details[threat_type] = []
                                threat_details[threat_type].append(current_path)
                
                elif isinstance(value, dict):
                    for nested_key, nested_value in value.items():
                        check_value(nested_key, nested_value, current_path)
                
                elif isinstance(value, list):
                    for i, item in enumerate(value):
                        check_value(f"[{i}]", item, current_path)
            
            # Check all input data
            for key, value in input_data.items():
                check_value(key, value)
            
            is_safe = len(threats_found) == 0
            risk_level = "low" if is_safe else "high" if len(threats_found) > 3 else "medium"
            
            result = {
                "is_safe": is_safe,
                "risk_level": risk_level,
                "threats_found": threats_found,
                "threat_summary": threat_details,
                "threat_count": len(threats_found)
            }
            
            logger.info(
                "Security threat check completed",
                action="check_security",
                output_data={
                    "is_safe": is_safe,
                    "risk_level": risk_level,
                    "threat_count": len(threats_found)
                },
                layer="validation_utils",
                session_id=session_id
            )
            
            if not is_safe:
                logger.warning(
                    "Security threats detected in input",
                    action="check_security",
                    output_data={"threats": threat_details},
                    layer="validation_utils",
                    session_id=session_id
                )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Security check completed - {'Safe' if is_safe else f'{len(threats_found)} threats found'}",
                code=StatusCode.OK,
                outputs=result,
                meta={"is_safe": is_safe, "risk_level": risk_level}
            )
            
        except Exception as e:
            logger.error(
                "Error in security threat check",
                action="check_security",
                reason=str(e),
                layer="validation_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Security check failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )


# Convenience functions for easy access
def validate_input(input_data: Dict[str, Any], schema: Dict[str, Any], session_id: str = None) -> StateOutput:
    """Convenience function for input validation."""
    validator = InputValidator()
    return validator.validate_input(input_data, schema, session_id)


def sanitize_input(input_data: Dict[str, Any], session_id: str = None) -> StateOutput:
    """Convenience function for input sanitization."""
    validator = InputValidator()
    return validator.sanitize_input(input_data, session_id)


def check_security_threats(input_data: Dict[str, Any], session_id: str = None) -> StateOutput:
    """Convenience function for security threat checking."""
    validator = SecurityValidator()
    return validator.check_security_threats(input_data, session_id)
