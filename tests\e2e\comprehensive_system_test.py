#!/usr/bin/env python3
"""
Comprehensive System Test for Voice Agents Platform
Tests all major components after the file structure reorganization.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = str(Path(__file__).parent.parent.parent)
sys.path.insert(0, project_root)

def test_imports():
    """Test that all major components can be imported successfully."""
    print("🔍 Testing imports...")
    
    try:
        # Test base agent
        from agents.base.base_agent import BaseAgent
        print("✅ BaseAgent import successful")
        
        # Test STT agent
        from agents.stt.stt_agent import STTAgent
        print("✅ STTAgent import successful")
        
        # Test TTS agents
        from agents.tts.tts_agent import TTSAgent
        from agents.tts.tts_openai import TTSAgentOpenAI
        print("✅ TTS agents import successful")
        
        # Test processing agents
        from agents.processing.preprocessing_agent import PreprocessingAgent
        from agents.processing.processing_agent import ProcessingAgent
        print("✅ Processing agents import successful")
        
        # Test filler agent
        from agents.filler.filler_tts_agent import FillerTTSAgent
        print("✅ Filler agent import successful")
        
        # Test orchestration
        from agents.orchestration.orchestrator_agent import Orchestrator
        print("✅ Orchestrator import successful")
        
        # Test core components
        from core.session.session_manager import SessionManager
        from core.state_manager.state_manager import StateManager
        print("✅ Core components import successful")
        
        # Test memory components
        from core.memory.redis_context import RedisClient
        from core.memory.mongo_client import get_mongo_client
        from core.memory.memory_manager import MemoryManager
        print("✅ Memory components import successful")
        
        # Test logging
        from core.logging.logger_config import setup_development_logging, get_module_logger
        print("✅ Logging components import successful")
        
        # Test schemas
        from schemas.outputSchema import StateOutput, StatusType, StatusCode
        from schemas.a2a_message import A2AMessage, MessageType
        from schemas.agent_metadata import AgentMetadata
        print("✅ Schema components import successful")
        
        # Test utilities
        from utils.audio_utils import synthesize_fallback_audio
        from utils.language_utils import detect_language
        from utils.validators import validate_input, sanitize_input, check_security_threats
        print("✅ Utility components import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_logging_system():
    """Test the logging system functionality."""
    print("\n🔍 Testing logging system...")
    
    try:
        from core.logging.logger_config import setup_development_logging, get_module_logger
        
        # Setup logging
        setup_development_logging()
        logger = get_module_logger("test_module")
        
        # Test logging
        logger.info("Test log message")
        logger.warning("Test warning message")
        
        print("✅ Logging system working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Logging test failed: {e}")
        return False

def test_main_entry_point():
    """Test the main entry point."""
    print("\n🔍 Testing main entry point...")
    
    try:
        import subprocess
        import sys
        
        # Run the main entry point
        result = subprocess.run([sys.executable, "core/main.py"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Main entry point runs successfully")
            return True
        else:
            print(f"❌ Main entry point failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✅ Main entry point started (timeout expected)")
        return True
    except Exception as e:
        print(f"❌ Main entry point test failed: {e}")
        return False

def test_file_structure():
    """Test that the new file structure is correct."""
    print("\n🔍 Testing file structure...")
    
    expected_dirs = [
        "agents/base",
        "agents/stt", 
        "agents/tts",
        "agents/processing",
        "agents/orchestration",
        "agents/filler",
        "core/session",
        "core/state_manager", 
        "core/orchestrator",
        "core/memory",
        "core/logging",
        "configs",
        "data/filler_words",
        "data/audio_samples", 
        "data/prompts",
        "docs",
        "tests/unit",
        "tests/integration",
        "tests/e2e",
        "workflows",
        "schemas",
        "scripts",
        "utils",
        "secrets"
    ]
    
    missing_dirs = []
    for dir_path in expected_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"❌ Missing directories: {missing_dirs}")
        return False
    else:
        print("✅ All expected directories exist")
        return True

def main():
    """Run all tests."""
    print("🚀 Starting Comprehensive System Test")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Component Imports", test_imports),
        ("Logging System", test_logging_system),
        ("Main Entry Point", test_main_entry_point),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED! The reorganization was successful!")
        return 0
    else:
        print(f"\n⚠️  {len(results) - passed} tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
