import asyncio
import os
import sys
from pathlib import Path

# Add project root to path
project_root = str(Path(__file__).parent.parent.parent)
sys.path.insert(0, project_root)

# Load environment variables BEFORE importing agents
from dotenv import load_dotenv

load_dotenv()

from agents.tts.tts_openai import TTSAgentOpenAI

async def main():
    session_id = "test_session_tts"
    state_id = "test_state"
    agent = TTSAgentOpenAI(session_id=session_id, state_id=state_id)
    # Simulate context with emotion and gender
    test_text = "Hello, this is <PERSON><PERSON>, welcome!"
    context = {
        "emotion": "happy",
        "gender": "female",
        "llm_answer": test_text
    }
    # Save context to Redis
    await agent.save_context(session_id, context)
    # Run the agent
    print("Running TTSAgent...")
    result = await agent.process({})
    print("[TTSAgent Output]:", result.model_dump())
    # Print audio path
    audio_path = result.outputs.get("audio_path")
    print("[Audio Path]:", audio_path)
    # Play the audio if possible
    try:
        from playsound import playsound
        print("Playing audio...")
        playsound(audio_path)
    except ImportError:
        print("playsound not installed. Please play the audio file manually:", audio_path)
    except Exception as e:
        print(f"Could not play audio: {e}\nPlease play the file manually: {audio_path}")
    # Print updated context
    updated_context = await agent.load_context(session_id)
    print("[Updated Context]:", updated_context)

if __name__ == "__main__":
    asyncio.run(main()) 