# Voice Agents Platform - Repository Structure Documentation

This document provides a comprehensive overview of the Voice Agents Platform repository structure after the major reorganization completed on 2025-07-10.

## 📁 Repository Overview

The Voice Agents Platform has been reorganized into a clean, modular architecture with the following key improvements:

- **🤖 Agents by Function**: All AI agents are organized by their specific functionality
- **🏗️ Core Infrastructure**: Runtime logic separated into specialized modules
- **⚙️ Centralized Configuration**: All config files consolidated in `configs/`
- **📊 Data Management**: Assets and resources organized in `data/`
- **🧪 Comprehensive Testing**: Tests organized by type (unit/integration/e2e)
- **📚 Better Documentation**: Centralized docs with architecture guides
- **🔒 Security**: Sensitive files properly isolated in `secrets/`

## ✅ Reorganization Validation

The reorganization has been validated with comprehensive testing:
- ✅ All major components can be imported successfully
- ✅ Logging system functions correctly from new locations
- ✅ Main entry point runs without errors
- ✅ File structure matches design specifications
- ✅ Import paths updated throughout codebase

## 🏗️ Root Level Files

### Configuration Files
- **`README.md`** - Main project documentation with architecture overview
- **`docker-compose.yml`** - Docker orchestration for Redis stack server (ports 6379, 8001)
- **`environment.yml`** - Conda environment specification with Python 3.13 and dependencies (OpenAI, Pydantic, Redis, Qdrant, etc.)

## 📂 Directory Structure

### 🧠 `agents/` - Core AI Agents
Contains the main AI agents responsible for different aspects of voice processing.

#### Files:
- **`README.md`** - Documentation for agent implementations
- **`base_agent.py`** - Abstract base classes for all agent types:
  - `BaseAgent` - Core agent interface with logging and context management
  - `AudioAgent` - Speech-to-text, text-to-speech, voice activity detection
  - `DisambiguatorAgent` - Language detection and text normalization
  - `ResponseAgent` - Intent parsing, RAG queries, LLM responses
  - `MCPAgent` - Modular Communication Protocol agent interface

#### Subdirectories:
- **`Layer2/`** - Concrete implementations of Layer2 pipeline agents:
  - `stt_agent.py` - **Speech-to-Text Agent**
    - Uses OpenAI Whisper-1 model for transcription
    - Supports multiple languages (Arabic, English, etc.)
    - Implements retry logic with tenacity for reliability
    - Manages Redis context for session state
    - Handles audio file processing with temporary file management
    - Publishes A2A notifications on completion
  - `preprocessing_agent.py` - **Text Preprocessing Agent**
    - Cleans and normalizes transcribed text
    - Classifies user intent using GPT-4o-mini
    - Detects emotion and gender from text
    - Configurable feature toggles for emotion/gender detection
    - Saves processed data to Redis context
  - `processing_agent.py` - **Main Processing Agent**
    - Routes requests to internal logic, database, or RAG engine
    - Implements LLM post-processing for response generation
    - Manages conversation history in contextual memory
    - Handles Redis fallback scenarios
    - Supports multiple routing strategies based on content analysis
  - `tts_agent.py` - **Text-to-Speech Agent**
    - Uses ElevenLabs API for high-quality voice synthesis
    - Implements fallback audio generation when API fails
    - Supports voice configuration and customization
    - Manages audio file output and cleanup
  - `filler_tts_agent.py` - **Filler TTS Agent**
    - Generates filler audio during processing delays
    - Uses predefined Arabic filler texts
    - Leverages main TTS agent for audio synthesis
    - Provides user experience enhancement during wait times

### 🧩 `core/` - Infrastructure & Runtime Logic
The backbone of the system handling execution, memory, logging, and coordination.

#### Main Files:
- **`__main__.py`** - Main entry point for the application
- **`agent_registry.py`** - Agent registration and discovery system with Redis pub/sub
- **`exceptions.py`** - Custom exception hierarchy (VoiceAgentError, SchemaError, WorkflowError, etc.)
- **`logger.py`** - Structured JSONL logging system with thread-safe concurrent writes
- **`logger_config.py`** - Logger configuration settings
- **`logger_manual.md`** - Logging best practices guide
- **`contextual_memory_log.json`** - Contextual memory storage

#### Subdirectories:
- **`docs/`** - Core documentation
  - `state_output_guide.md` - Guide for state output handling
- **`logs/`** - Runtime log storage
  - `conversations/` - Conversation transcripts
  - `errors/` - Error logs
  - `metrics/` - Performance metrics
- **`state_mgmt/`** - State management system
  - `StateManager.py` - Main state manager for workflow execution
  - `WorkflowSchemaLoader.py` - Workflow configuration loader
  - `Layer2SchemaLoader.py` - Layer2 pipeline configuration loader
  - `base_state.py` - Base state interface
  - `layer2_pipeline.py` - Pipeline execution logic
  - `memory_manager.py` - Multi-layer memory management
  - `mcp_mock.py` - MCP protocol mock implementation
  - `states/` - Individual state implementations

### ⚙️ `config/` - Configuration Files
JSON configuration files for different system components.

#### Files:
- **`README.md`** - Configuration documentation
- **`state_manager_config.json`** - State manager configuration
- **`l2_greeting.json`** - Layer2 greeting pipeline configuration
- **`l2_goodbye.json`** - Layer2 goodbye pipeline configuration
- **`l2_check_balance.json`** - Layer2 balance check pipeline configuration
- **`l2_fallback.json`** - Layer2 fallback pipeline configuration

### 📊 `schemas/` - Data Models & Validation
Pydantic models for data validation and structure.

#### Files:
- **`__init__.py`** - Package initialization
- **`a2a_message.py`** - Agent-to-Agent message schema with MessageType enum
- **`agent_metadata.py`** - Agent metadata schema for registration
- **`outputSchema.py`** - Output schema definitions for state management

### 🛠️ `utils/` - Utility Functions
Shared helper functions and utilities used across the system.

#### Files:
- **`README.md`** - Utilities documentation
- **`audio_utils.py`** - Audio processing utilities (VAD, noise filtering, normalization)
- **`language_utils.py`** - Language detection, gender detection, translation helpers
- **`redis_client.py`** - Redis client wrapper with async support
- **`mongo_client.py`** - MongoDB client utility for voice agents database connection
- **`validation_utils.py`** - Data validation utilities

### 📜 `scripts/` - CLI Tools & Utilities
Command-line tools for development, testing, and maintenance.

#### Files:
- **`README.md`** - Scripts documentation
- **`redis_usage_example.py`** - Redis client usage examples
- **`registry_demo.py`** - Agent registry demonstration script

#### Subdirectories:
- **`Layer2/`** - Test scripts for Layer2 agents:
  - `test_stt_agent_mp3.py` - **STT Agent Testing**
    - Tests speech-to-text with Arabic MP3 files from fillerWords directory
    - Demonstrates audio file processing and Redis context management
    - Shows transcription results and latency metrics
  - `test_preprocessing_agent.py` - **Preprocessing Agent Testing**
    - Tests text cleaning, intent classification, and emotion detection
    - Simulates Redis context loading and saving
    - Demonstrates GPT-4o-mini integration for NLP tasks
  - `test_processing_agent.py` - **Processing Agent Testing**
    - Tests routing logic for different query types (account, database, knowledge)
    - Demonstrates LLM post-processing and conversation memory
    - Shows Redis context management and fallback handling
  - `test_tts_agent.py` - **TTS Agent Testing**
    - Tests ElevenLabs API integration for voice synthesis
    - Demonstrates audio file generation and management
    - Shows fallback audio synthesis capabilities
  - `test_filler_tts_agent.py` - **Filler TTS Testing**
    - Tests filler audio generation with random text selection
    - Includes audio playback functionality for testing
    - Demonstrates user experience enhancement features
  - `test_agents_pipeline.py` - **Complete Pipeline Testing**
    - Orchestrates full agent coordination via Redis pub/sub
    - Tests A2A message communication between agents
    - Demonstrates real-time agent task management and coordination
    - Simulates production-like agent interaction patterns

### ✅ `tests/` - Test Suite
Unit tests and integration tests for the platform.

#### Files:
- **`README.md`** - Testing documentation
- **`logger_test.py`** - Logger functionality tests
- **`test_base_agent.py`** - Unit tests for BaseAgent class with mock implementations and async testing

### 🪵 `logs/` - Runtime Logs
Centralized logging directory for all system logs.

#### Subdirectories:
- **`conversations/`** - Full conversation transcripts indexed by session ID
- **`errors/`** - Error logs with stack traces and metadata
- **`metrics/`** - Performance metrics (token usage, latency, session duration)

### 🌐 `frontend/` - User Interface
Web-based visualization and debugging tools.

#### Files:
- **`README.md`** - Frontend documentation (currently placeholder)

### 📚 `docs/` - Documentation
Additional documentation and guides.

#### Files:
- **`LOGGING_INTEGRATION_GUIDE.md`** - Comprehensive guide for integrating the custom logging system

### 🔄 `workflows/` - Workflow Definitions
Directory for workflow configuration files (currently empty).

### 💡 `examples/` - Example Implementations
Example code and usage patterns (currently empty).

### 🎵 `fillerWords/` - Audio Filler Files
Pre-recorded Arabic audio files used as filler sounds during processing delays.

#### Files:
- **`ثانية واحدة.mp3`** - "One second" filler audio
- **`ثانية يا فندم.mp3`** - "One second, sir" filler audio
- **`لحضة واحدة مع حضرتك.mp3`** - "One moment with you" filler audio
- **`لحظات و هرجع لحضرتك.mp3`** - "Moments and I'll get back to you" filler audio
- **`ممم.mp3`** - "Hmm" filler audio

### 🎧 Audio Output Files
Generated TTS audio files from testing and runtime operations.

#### Files:
- **`tts_fallback_openai_redis_fallback.mp3`** - Fallback TTS audio when Redis is unavailable
- **`tts_output_test_session_tts.mp3`** - Test session TTS output file

## 🔧 Key Technologies & Dependencies

### Core Dependencies:
- **Python 3.13** - Main runtime
- **Redis** - Message bus and session storage
- **MongoDB** - Database for voice agents data storage
- **Pydantic** - Data validation and serialization
- **OpenAI** - LLM integration and Whisper STT
- **ElevenLabs** - Text-to-speech synthesis
- **Qdrant** - Vector database for RAG
- **NumPy** - Numerical computations
- **HTTPX** - Async HTTP client
- **Tenacity** - Retry logic for API calls
- **PyMongo** - MongoDB client library

### Development Tools:
- **Docker & Docker Compose** - Containerization
- **Conda** - Environment management
- **Pytest** - Testing framework

## 🏛️ Architecture Patterns

1. **Agent-Based Architecture** - Modular agents with specific responsibilities
2. **Layer2 Pipeline** - Concrete agent implementations for voice processing pipeline (STT → Preprocessing → Processing → TTS)
3. **Event-Driven Communication** - Redis pub/sub for agent coordination and A2A messaging
4. **State Management** - Centralized state management with workflow execution
5. **Structured Logging** - JSONL logging with rich metadata and performance tracking
6. **Memory Layers** - Multi-tier memory management (ephemeral, session, persistent)
7. **Schema-Driven** - Pydantic models for type safety and validation
8. **Retry Logic** - Tenacity-based retry mechanisms for API reliability
9. **Fallback Systems** - Graceful degradation when external services fail
10. **Multilingual Support** - Arabic and English language processing capabilities

## 🚀 Getting Started

1. **Environment Setup**: Use `environment.yml` to create conda environment
2. **Services**: Start Redis with `docker-compose up -d`
3. **Configuration**: Review and customize JSON configs in `config/`
4. **Logging**: Follow `docs/LOGGING_INTEGRATION_GUIDE.md` for logging setup
5. **Testing**: Run tests in `tests/` directory

This repository structure supports scalable voice AI applications with clear separation of concerns, robust error handling, and comprehensive logging for production deployments.
