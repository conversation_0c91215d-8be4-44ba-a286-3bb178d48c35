import asyncio
from core.memory.mongo_retrieval import (
    get_user_from_mongo,
    get_call_session_from_mongo,
    get_pipeline_from_mongo,
    get_intent_history_from_mongo,
    get_dialog_log_from_mongo,
)

async def main():
    # You can change these to match your test data
    session_id = input("Enter session_id to retrieve: ") or "test_session_001"
    user_id = input("Enter user_id to retrieve: ") or "test_user_001"
    pipeline_id = input("Enter pipeline_id to retrieve: ") or "test_pipeline_001"

    print(f"\n[TEST] Retrieving user: {user_id}")
    user = await get_user_from_mongo(user_id)
    print(user)

    print(f"\n[TEST] Retrieving call session: {session_id}")
    call_session = await get_call_session_from_mongo(session_id)
    print(call_session)

    print(f"\n[TEST] Retrieving pipeline: {pipeline_id}")
    pipeline = await get_pipeline_from_mongo(pipeline_id)
    print(pipeline)

    print(f"\n[TEST] Retrieving intent history for session: {session_id}")
    intent_history = await get_intent_history_from_mongo(session_id)
    print(intent_history)

    print(f"\n[TEST] Retrieving dialog log for session: {session_id}")
    dialog_log = await get_dialog_log_from_mongo(session_id)
    print(dialog_log)

if __name__ == "__main__":
    asyncio.run(main()) 