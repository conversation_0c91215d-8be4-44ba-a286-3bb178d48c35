"""
Custom exceptions for the voice agent platform.
"""

class VoiceAgentError(Exception):
    """Base exception for all voice agent errors."""
    pass

class SchemaError(VoiceAgentError):
    """Raised when there's an issue with schema validation."""
    pass

class WorkflowError(VoiceAgentError):
    """Raised when there's an issue with workflow execution."""
    pass

class Layer2Error(VoiceAgentError):
    """Raised when there's an issue with Layer2 execution."""
    pass

class StateTransitionError(WorkflowError):
    """Raised when there's an issue with state transitions."""
    pass

class PipelineError(Layer2Error):
    """Raised when there's an issue with pipeline execution."""
    pass

class ConfigurationError(VoiceAgentError):
    """Raised when there's an issue with configuration files."""
    pass