import asyncio
import uuid
import os
import pytest
from agents.orchestration.orchestrator_agent import Orchestrator
from agents.stt.stt_agent import STTAgent
from agents.processing.preprocessing_agent import PreprocessingAgent
from agents.processing.processing_agent import ProcessingAgent
from agents.tts.tts_agent import TTSAgent
from core.memory.redis_context import RedisClient
from schemas.a2a_message import A2AMessage, MessageType
from core.memory.memory_manager import MemoryManager

# All audio files in data/filler_words directory
FILLER_AUDIO_FILES = [
    os.path.join("data/filler_words", f) for f in os.listdir("data/filler_words")
    if f.lower().endswith(".mp3")
]

@pytest.mark.asyncio
@pytest.mark.integration
@pytest.mark.parametrize("audio_file", FILLER_AUDIO_FILES)
async def test_orchestrator_with_fillerwords(audio_file):
    session_id = f"test_fillerwords_{uuid.uuid4().hex[:8]}"
    print(f"\n=== Integration Test: {session_id} | Audio: {audio_file} ===")

    orchestrator = Orchestrator(workflow_name="GenericBank.json")
    orch_task = asyncio.create_task(orchestrator.start())
    await asyncio.sleep(1)

    stt_task = asyncio.create_task(agent_runner(STTAgent, "stt_agent", session_id))
    pre_task = asyncio.create_task(agent_runner(PreprocessingAgent, "preprocessing_agent", session_id))
    proc_task = asyncio.create_task(agent_runner(ProcessingAgent, "processing_agent", session_id))
    tts_task = asyncio.create_task(agent_runner(TTSAgent, "tts_agent", session_id))
    await asyncio.sleep(1)

    redis = RedisClient()
    first_instruction = A2AMessage(
        session_id=session_id,
        message_type=MessageType.INSTRUCTION,
        source_agent="System",
        target_agent="stt_agent",
        payload={"action": "process_audio", "audio_path": audio_file}
    )
    await redis.publish(f"agent:stt_agent:{session_id}", first_instruction.to_json())

    try:
        await asyncio.wait_for(asyncio.sleep(12), timeout=15)
    except asyncio.TimeoutError:
        pass
    finally:
        for task in [stt_task, pre_task, proc_task, tts_task, orch_task]:
            task.cancel()
        await asyncio.gather(stt_task, pre_task, proc_task, tts_task, orch_task, return_exceptions=True)

    memory = MemoryManager(session_id)
    print("\n=== Final Contextual Memory ===")
    print(await memory.get_all_contextual())
    print("\n=== Conversation History ===")
    print(await memory.get_conversation())
    print(f"\n[TEST] Finished integration for session: {session_id}\n{'='*60}\n")

async def agent_runner(agent_class, agent_name, session_id):
    redis = RedisClient()
    instruction_channel = f"agent:{agent_name}:{session_id}"
    print(f"[{agent_name}] Listening on '{instruction_channel}'")

    async def callback(message_json):
        message = A2AMessage.from_json(message_json)
        print(f"[{agent_name}] Received instruction: {message.payload}")
        agent = agent_class(session_id=session_id, state_id="test_state")
        if agent_name == "stt_agent":
            audio_path = message.payload.get("audio_path")
            with open(audio_path, "rb") as f:
                audio_bytes = f.read()
            result = await agent.process(audio_bytes, session_context={"language": "ar", "response_format": "text"})
        else:
            result = await agent.process({})
        print(f"[{agent_name} Output]:", result.model_dump())

    try:
        await redis.subscribe(instruction_channel, callback)
    except asyncio.CancelledError:
        print(f"[{agent_name}] Task complete, shutting down listener.")
    finally:
        await redis.close() 