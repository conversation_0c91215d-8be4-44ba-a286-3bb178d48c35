import sys
import asyncio
import os
import time
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.state_manager.state_manager import StateManager
from asteval import Interpreter
from core.logging.logger_config import setup_development_logging, get_module_logger, cleanup_logger
from core.memory.redis_context import RedisClient

# Load variables from .env file into environment
load_dotenv()

# Set up logging for the main module
setup_development_logging()
logger = get_module_logger("main", session_id="demo_session")

def test_state_output_success():
    return StateOutput(
        status=StatusType.SUCCESS,
        message="Operation successful",
        code=StatusCode.OK,
        outputs={"data": "test"},
        meta={"source": "test_suite"}
    )

async def run_trial(sessionId: str = "test_session", userId: str = "user_1"):
    """
    Runs a simple trial of the StateManager with the GenericBank workflow.
    """

    try:
        sm = await StateManager.create("GenericBank.json", sessionId, userId)
        # Step 1: Simulate greeting state
        logger.info(
            "=== Step 1: Greeting State ===",
            action="step_1_greeting",
            layer="demo"
        )
        input_data = {"intent": "check_balance", "slots": {"account_id": "12345"}}
        result = await sm.execute_step(input_data)
        await sm.transition("state_check_balance")
        logger.info(
            "Step 1 completed",
            action="step_1_greeting",
            output_data={
                "result_status": str(result.status),
                "current_state": sm.current_state_id
            },
            layer="demo"
        )

        # Step 2: Simulate check balance state
        logger.info(
            "=== Step 2: Check Balance State ===",
            action="step_2_check_balance",
            layer="demo"
        )
        input_data = {"account_id": "12345"}
        result = await sm.execute_step(input_data)
        await sm.transition("state_goodbye")
        logger.info(
            "Step 2 completed",
            action="step_2_check_balance",
            output_data={
                "result_status": str(result.status),
                "current_state": sm.current_state_id
            },
            layer="demo"
        )

        # Step 3: Simulate goodbye state
        logger.info(
            "=== Step 3: Goodbye State ===",
            action="step_3_goodbye",
            layer="demo"
        )
        input_data = {}
        result = await sm.execute_step(input_data)
        logger.info(
            "Step 3 completed",
            action="step_3_goodbye",
            output_data={
                "result_status": str(result.status),
                "current_state": sm.current_state_id
            },
            layer="demo"
        )

        logger.info(
            "StateManager trial run completed successfully",
            action="run_trial",
            layer="demo"
        )

    except Exception as e:
        logger.error(
            "Error during StateManager trial run",
            action="run_trial",
            reason=str(e),
            layer="demo"
        )
        raise

async def run_agents_trial(sessionId: str = "test_session", userId: str = "user_1"):
    try:
        sm = await StateManager.create("AgentsTry.json", sessionId, userId)
        input_data = {"filler_text": "this is a testing messace, called filler test message", "text": "this is a testing messace, called agent test message"}
        result = await sm.execute_step(input_data)
        logger.info(
            "StateManager trial run completed successfully",
            action="run_trial",
            layer="demo"
        )
    except Exception as e:
        logger.error(
            "Error during StateManager trial run",
            action="run_trial",
            reason=str(e),
            layer="demo"
        )
        raise

async def run_2_trials():
    try:
        # Run both trials concurrently
        await asyncio.gather(
            run_trial("test_session_1", "user_1"),
            # run_trial("test_session_2", "user_2")
        )
    except Exception as e:
        logger.error(
            "Error during StateManager trial run",
            action="run_trial",
            reason=str(e),
            layer="demo"
        )
        raise

if __name__ == "__main__":
    try:
        logger.info(
            "Starting Voice Agents Platform demo",
            action="main",
            layer="demo"
        )
        asyncio.run(run_agents_trial())
        logger.info(
            "Voice Agents Platform demo completed",
            action="main",
            layer="demo"
        )
    except Exception as e:
        logger.error(
            "Error in Voice Agents Platform demo",
            action="main",
            reason=str(e),
            layer="demo"
        )
        raise
    finally:
        # Clean up logger resources
        cleanup_logger()


