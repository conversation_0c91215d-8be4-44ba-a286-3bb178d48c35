{"id": "l2_inquiry_banking_system_v2", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio_path": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT"}}, {"step": "preprocessing", "process": "preprocessing_process", "agent": "preprocessing_agent", "input": {"transcript": "text"}, "tools": {"external_tools": "openai"}, "output": {"emotion": "emotion", "intent": "intent", "gender": "gender", "latencyPreprocessing": "latencyPreprocessing", "clean_text": "clean_text"}}, {"step": "filler_tts", "process": "filler_tts_process", "agent": "filler_tts_agent", "input": {}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "filler_tts_audio_path", "latencyFillerTTS": "latencyFillerTTS"}}, {"step": "processing", "process": "processing_process", "agent": "processing_agent", "input": {"clean_text": "clean_text", "intent": "intent"}, "tools": {"external_tools": "openai"}, "output": {"llm_answer": "llm_answer", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "llm_answer", "emotion": "emotion", "gender": "gender"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "tts_audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}