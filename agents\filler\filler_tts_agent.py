from agents.base.base_agent import BaseAgent
from agents.tts.tts_agent import TTSAgent
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
import random

class FillerTTSAgent(BaseAgent):
    FILLER_TEXTS = [
        "Just a moment, please...",
        "Let me check that for you...",
        "Give me a second...",
        "I'm working on it, please wait..."
    ]

    def __init__(self, session_id=None, state_id=None):
        super().__init__("filler_tts_agent", session_id, state_id)

    async def process(self, input_data, context=None):
        # Pick a filler text (or use provided one)
        filler_text = input_data.get("filler_text") if input_data else None
        if not filler_text:
            filler_text = random.choice(self.FILLER_TEXTS)
        # Call TTSAgent to synthesize the filler text
        tts_agent = TTSAgent(session_id=self.session_id, state_id=self.state_id)
        tts_result = await tts_agent.process({"text": filler_text}, context=context)
        # Optionally, notify frontend
        notification = A2AMessage(
            session_id=self.session_id,
            message_type=MessageType.NOTIFICATION,
            source_agent=self.agent_name,
            target_agent="Orchestrator",
            payload={"status": "filler", "filler_text": filler_text, "audio_path": tts_result.outputs.get("audio_path")}
        )
        # await self.publish_notification("agent_completion", notification.to_json())
        return StateOutput(
            status=StatusType.SUCCESS,
            message="Filler TTS played",
            code=StatusCode.OK,
            outputs={"audio_path": tts_result.outputs.get("audio_path"), "filler_text": filler_text},
            meta={"agent": self.agent_name}
        ) 