from pathlib import Path
import sys
import unittest
import os
import json
import shutil
import threading
import time
import logging

project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logging.logger import Logger, StateOutput

class TestLogger(unittest.TestCase):
    def setUp(self):
        """Set up test environment with a temporary log directory in the current working directory."""
        # Set log_dir to the current working directory
        self.log_dir = os.path.join(os.getcwd(), "voice_agent_test_logs")
        logging.getLogger().handlers.clear()
        logging.getLogger("voice_agent").handlers.clear()
        if os.path.exists(self.log_dir):
            for attempt in range(3):
                try:
                    shutil.rmtree(self.log_dir)
                    break
                except PermissionError:
                    time.sleep(0.2)
        for log_type in ["conversations", "metrics", "errors"]:
            log_path = os.path.join(self.log_dir, log_type)
            os.makedirs(log_path, exist_ok=True)
            log_file = os.path.join(log_path, f"{log_type}.jsonl")
            if os.path.exists(log_file):
                try:
                    os.remove(log_file)
                except PermissionError:
                    pass
            for f in os.listdir(log_path):
                if f.startswith(f"{log_type}.jsonl"):
                    try:
                        os.remove(os.path.join(log_path, f))
                    except PermissionError:
                        pass
        self.logger = Logger(log_dir=self.log_dir, verbosity="debug", max_bytes=10000, backup_count=2)
        self.session_id = "test_session_123"
        self.state_id = "test_state_001"
        self.trace_id = "test_trace_456"

    def tearDown(self):
        """Clean up test log directory with retry mechanism."""
        self.logger.flush()  # Ensure logs are flushed before closing
        self.logger.close()
        time.sleep(0.2)  # Reduced sleep
        print(f"Active threads after close: {[t.name for t in threading.enumerate()]}")
        if os.path.exists(self.log_dir):
            for attempt in range(3):
                try:
                    shutil.rmtree(self.log_dir)
                    break
                except PermissionError:
                    time.sleep(0.2)  # Reduced sleep
                    if attempt == 2:
                        for root, _, files in os.walk(self.log_dir):
                            for f in files:
                                try:
                                    os.unlink(os.path.join(root, f))
                                except PermissionError:
                                    pass
                        try:
                            shutil.rmtree(self.log_dir)
                        except PermissionError as e:
                            print(f"Failed to delete {self.log_dir}: {e}")

    def _read_log_file(self, log_type: str) -> list:
        """Read all log entries from a specific log file with retry."""
        log_file = os.path.join(self.log_dir, log_type, f"{log_type}.jsonl")
        if not os.path.exists(log_file):
            return []
        for attempt in range(3):
            try:
                with open(log_file, "r", encoding="utf-8") as f:
                    return [json.loads(line) for line in f]
            except (PermissionError, IOError):
                print(f"Retry {attempt + 1} reading {log_file} due to error")
                time.sleep(0.2)
        print(f"Failed to read {log_file} after 3 attempts")
        return []

    def test_log_success(self):
        """Test successful log entry with decorator."""
        @self.logger.log_decorator
        def sample_function(session_id, state_id):
            return {"result": "success"}

        sample_function(session_id=self.session_id, state_id=self.state_id)
        self.logger.flush()
        time.sleep(0.05)  # Reduced sleep
        logs = self._read_log_file("metrics")
        self.assertGreater(len(logs), 0)
        log_entry = logs[0]
        self.assertEqual(log_entry["session_id"], self.session_id)
        self.assertEqual(log_entry["state_id"], self.state_id)
        self.assertEqual(log_entry["status"], "success")
        self.assertIn("timestamp", log_entry)
        self.assertIn("metrics", log_entry)

    def test_log_state_output(self):
        """Test logging StateOutput object."""
        state_output = StateOutput(status="success", data={"text": "test"}, metadata={"lang": "en"})
        self.logger.log_state_output(
            level="info",
            message="Test StateOutput",
            session_id=self.session_id,
            state_id=self.state_id,
            layer="workflow",
            step="test_step",
            custom_module="test_module",
            agent_name="TestAgent",
            action="test_action",
            state_output=state_output,
            input_data={"input": "test"},
            trace_id=self.trace_id
        )
        self.logger.flush()
        time.sleep(0.05)  # Reduced sleep
        logs = self._read_log_file("conversations")
        self.assertGreater(len(logs), 0)
        log_entry = logs[0]
        self.assertEqual(log_entry["status"], "success")
        self.assertEqual(log_entry["output"]["data"], {"text": "test"})
        self.assertEqual(log_entry["output"]["metadata"], {"lang": "en"})

    def test_log_error(self):
        """Test error logging with decorator."""
        @self.logger.log_decorator
        def failing_function(session_id, state_id):
            raise ValueError("Test error")

        with self.assertRaises(ValueError):
            failing_function(session_id=self.session_id, state_id=self.state_id)
        self.logger.flush()
        time.sleep(0.05)  # Reduced sleep
        logs = self._read_log_file("errors")
        self.assertGreater(len(logs), 0)
        log_entry = logs[0]
        self.assertEqual(log_entry["status"], "fail")
        self.assertEqual(log_entry["reason"], "Test error")

    def test_verbosity_filtering(self):
        """Test that logs respect verbosity levels."""
        logging.getLogger("voice_agent").handlers.clear()
        logger = Logger(log_dir=self.log_dir, verbosity="error")
        logger.log(
            level="debug",
            message="Should not log",
            session_id=self.session_id,
            state_id=self.state_id,
            layer="test",
            step="test_step",
            custom_module="test_module",
            agent_name="TestAgent",
            action="test_action",
            input_data={},
            output_data={},
            status="success"
        )
        logger.flush()
        time.sleep(0.05)  # Reduced sleep
        logs = self._read_log_file("metrics")
        self.assertEqual(len(logs), 0)

        logger.log(
            level="error",
            message="Should log",
            session_id=self.session_id,
            state_id=self.state_id,
            layer="test",
            step="test_step",
            custom_module="test_module",
            agent_name="TestAgent",
            action="test_action",
            input_data={},
            output_data={},
            status="fail"
        )
        logger.flush()
        time.sleep(0.05)  # Reduced sleep
        logs = self._read_log_file("errors")
        self.assertGreater(len(logs), 0)
        logger.close()

    def test_log_rotation(self):
        """Test log file rotation."""
        for i in range(200):
            self.logger.log(
                level="info",
                message=f"Test log {i}" * 50,
                session_id=self.session_id,
                state_id=self.state_id,
                layer="test",
                step="test_step",
                custom_module="test_module",
                agent_name="TestAgent",
                action="test_action",
                input_data={"index": i},
                output_data={"result": "test"},
                status="success",
                metrics={"duration_ms": 10}
            )
        self.logger.flush()
        time.sleep(0.1)  # Reduced sleep
        files = os.listdir(os.path.join(self.log_dir, "metrics"))
        self.assertTrue(any(f.startswith("metrics.jsonl.") for f in files))

    def _read_log_file(self, log_type: str) -> list:
        """Read all log entries from the main and rotated log files for the given log_type."""
        log_path = os.path.join(self.log_dir, log_type)
        # Find all files matching the log type (e.g., metrics.jsonl, metrics.jsonl.1)
        log_files = sorted([f for f in os.listdir(log_path) if f.startswith(f"{log_type}.jsonl")], reverse=True)
        all_logs = []
        for log_file in log_files:
            full_path = os.path.join(log_path, log_file)
            try:
                with open(full_path, "r", encoding="utf-8") as f:
                    all_logs.extend([json.loads(line) for line in f])
                print(f"Read {len(all_logs)} logs from {full_path}")
            except (PermissionError, IOError) as e:
                print(f"Error reading {full_path}: {e}")
        return all_logs

    def test_concurrent_logging(self):
        """Test thread-safe logging with fewer threads and logs."""
        def log_thread(i):
            for _ in range(10):  # 10 logs per thread
                self.logger.log(
                    level="info",
                    message=f"Thread {i} log",
                    session_id=f"session_{i}",
                    state_id=self.state_id,
                    layer="test",
                    step="test_step",
                    custom_module="test_module",
                    agent_name="TestAgent",
                    action="test_action",
                    input_data={"thread": i},
                    output_data={"result": "test"},
                    status="success",
                    metrics={"duration_ms": 10}
                )

        threads = [threading.Thread(target=log_thread, args=(i,)) for i in range(3)]
        for t in threads:
            t.start()
        for t in threads:
            t.join()
        self.logger.flush()
        time.sleep(0.5)  # Wait for file system to catch up
        print(f"Record counts after flush: {self.logger.record_count}")
        
        # Read logs from all files
        logs = self._read_log_file("metrics")
        conv_logs = self._read_log_file("conversations")
        print(f"Total metrics logs read: {len(logs)}")
        print(f"Total conversation logs read: {len(conv_logs)}")
        
        # Assert the total number of logs
        self.assertEqual(len(logs), 30)  # 3 threads * 10 logs
        self.assertEqual(len(conv_logs), 30)
        session_ids = {log["session_id"] for log in logs}
        self.assertEqual(len(session_ids), 3)  # 3 unique session IDs