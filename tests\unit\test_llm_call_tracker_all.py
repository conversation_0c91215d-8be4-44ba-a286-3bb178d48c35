import asyncio
from utils.llm_call_tracker import track_llm_call, calculate_cost, MODEL_PRICING

class MockMemoryManager:
    def __init__(self):
        self.contextual_data = {}
        self.contextual = self.Contextual(self)
    class Contextual:
        def __init__(self, parent):
            self.parent = parent
        async def get(self, key):
            return self.parent.contextual_data.get(key)
        async def set(self, key, value):
            self.parent.contextual_data[key] = value

def print_call(call):
    print(f"Model: {call['model']} | Context: {call['context']}")
    print(f"  Input tokens: {call['input_tokens']}")
    print(f"  Output tokens: {call['output_tokens']}")
    print(f"  Cost: ${call['cost_usd']:.6f}")
    if 'audio_duration_sec' in call:
        print(f"  Audio duration: {call['audio_duration_sec']:.2f}s")
    if 'character_count' in call:
        print(f"  Character count: {call['character_count']}")
    print(f"  Timestamp: {call['timestamp']}")
    print()

def print_grouped_turns(calls):
    from collections import defaultdict
    calls_by_turn = defaultdict(list)
    for call in calls:
        calls_by_turn[call.get("turn_id", 0)].append(call)
    for turn_id in sorted(calls_by_turn.keys()):
        print(f"Turn {turn_id}:")
        turn_total_cost = 0.0
        for call in calls_by_turn[turn_id]:
            model_type = call.get("context", "text")
            turn_total_cost += call.get("cost_usd", 0.0)
            if model_type == "stt":
                print(f"  STT: {call['model']} - Audio duration: {call.get('audio_duration_sec', '?'):.2f}s, Cost: ${call['cost_usd']:.6f}")
            elif model_type == "tts":
                print(f"  TTS: {call['model']} - Character count: {call.get('character_count', '?')}, Cost: ${call['cost_usd']:.6f}")
            else:
                context_label = call.get('context', 'LLM')
                print(f"  {context_label}: {call['model']} - In: {call['input_tokens']}, Out: {call['output_tokens']}, Cost: ${call['cost_usd']:.6f}")
        print(f"  Total turn cost: ${turn_total_cost:.6f}\n")

async def test_all():
    mm = MockMemoryManager()
    # Simulate a full user/AI turn: STT, LLM, TTS
    audio_path = "data/filler_words/user_conversation_part_1.mp3"
    # STT (user speaks)
    await track_llm_call(mm, "whisper-1", "", "This is a transcription.", "stt", audio_path=audio_path)
    # LLM (AI processes text)
    await track_llm_call(mm, "gpt-4o-mini", "This is a transcription.", "Hi there!", "chat")
    # TTS (AI responds with audio)
    await track_llm_call(mm, "elevenlabs", "Hi there!", "", "tts")
    # Show all tracked calls
    calls = await mm.contextual.get("llm_calls")
    print("\nGrouped Token Usage Details (per turn):")
    print_grouped_turns(calls)
    print("\nPricing Table:")
    for model, pricing in MODEL_PRICING.items():
        print(f"{model}: {pricing}")

if __name__ == "__main__":
    asyncio.run(test_all()) 