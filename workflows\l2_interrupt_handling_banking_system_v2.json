{"layer2_id": "l2_interrupt_handling_banking_system_v2", "description": "Layer2 configuration for interrupt handling in banking system", "version": "2.0", "pipeline": [{"step": "interrupt_detection", "agent": "interrupt_processor", "input": {"audio_data": "audio_data", "current_tts_audio_path": "current_tts_audio_path", "playback_position": "playback_position", "user_input": "user_input"}, "output": {"voice_activity": "voice_activity", "interrupt_detected": "interrupt_detected", "interrupt_confirmed": "interrupt_confirmed"}, "onError": {"handler": "error_manager", "fallback": "continue"}, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "interrupt_detection"}}, {"step": "reversibility_analysis", "agent": "action_reversibility_agent", "input": {"workflow_state_config": "workflow_state_config", "interrupt_context": "interrupt_context"}, "output": {"action_reversible": "action_reversible", "requires_confirmation": "requires_confirmation", "side_effect_severity": "side_effect_severity"}, "onError": {"handler": "error_manager", "fallback": "default_reversible"}}, {"step": "acknowledgment_generation", "agent": "acknowledgment_generator", "input": {"action_reversible": "action_reversible", "interrupt_config": "interrupt_config", "user_input": "user_input"}, "output": {"acknowledgment_message": "acknowledgment_message", "should_resume_tts": "should_resume_tts", "should_queue_input": "should_queue_input"}, "onError": {"handler": "error_manager", "fallback": "default_acknowledgment"}}, {"step": "context_storage", "agent": "memory_manager", "input": {"interrupt_detected": "interrupt_detected", "interrupt_confirmed": "interrupt_confirmed", "action_reversible": "action_reversible", "acknowledgment_message": "acknowledgment_message", "should_resume_tts": "should_resume_tts", "should_queue_input": "should_queue_input", "playback_position": "playback_position", "current_tts_audio_path": "current_tts_audio_path"}, "output": {"context_stored": "context_stored", "interrupt_event_logged": "interrupt_event_logged"}, "onError": {"handler": "error_manager", "fallback": "continue"}}, {"step": "tts_acknowledgment", "agent": "tts_agent", "input": {"text": "acknowledgment_message", "emotion": "neutral", "gender": "female"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}, "onError": {"handler": "error_manager", "fallback": "silent_acknowledgment"}, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "tts_acknowledgment"}}], "error_handling": {"default_strategy": "graceful_degradation", "max_retries": 2, "timeout_seconds": 10, "fallback_responses": {"default_reversible": "I understand you want to say something. Let me finish this first.", "default_acknowledgment": "I heard you. Let me complete this action first.", "silent_acknowledgment": ""}}, "interrupt_handling": {"enabled": true, "nested_interrupts": false, "max_interrupt_depth": 1, "grace_period_seconds": 0.5}, "memory_requirements": ["workflow_state_config", "interrupt_context", "current_tts_audio_path", "playback_position"], "output_validation": {"required_fields": ["interrupt_detected", "interrupt_confirmed", "acknowledgment_message", "action_reversible"], "optional_fields": ["should_resume_tts", "should_queue_input", "audio_path", "latencyTTS"]}}